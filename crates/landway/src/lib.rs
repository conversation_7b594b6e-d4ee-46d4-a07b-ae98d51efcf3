// MIT/Apache2 License

//! Dead-simple Wayland bindings for Rust.

#![deny(
    clippy::pedantic,
    missing_debug_implementations,
    missing_docs,
    clippy::exhaustive_enums,
    clippy::exhaustive_structs,
    clippy::float_arithmetic
)]
#![allow(
    clippy::manual_let_else, // We target lower MSRV's.
    clippy::unnested_or_patterns
)]

use std::any::Any;
use std::cell::Cell;
use std::env;
use std::ffi::{CStr, c_char, c_int, c_void};
use std::fmt;
use std::io;
use std::marker::PhantomData;
use std::mem;
use std::os::unix::io::{AsFd, AsRawFd, BorrowedFd, FromRawFd, OwnedFd, RawFd};
use std::os::unix::net::UnixStream;
use std::panic;
use std::path::{Path, PathBuf};
use std::ptr::{self, NonNull};
use std::rc::Rc;
use std::slice;
use std::sync::Arc;

use rustix::io as rio;

macro_rules! cstr {
    ($s:literal) => {{
        const LIT: &'static str = concat!($s, "\0");
        match ::std::ffi::CStr::from_bytes_with_nul(LIT.as_bytes()) {
            Ok(cstr) => cstr,
            Err(_) => unreachable!(),
        }
    }};
}

macro_rules! sig {
    ($s:literal) => {{
        match $crate::Signature::new(cstr!($s)) {
            Some(sig) => sig,
            None => unreachable!(),
        }
    }};
}

// Intentionally declared after the macros.
#[rustfmt::skip]
pub mod protocol;

//
// Wrapper over C API
//

/// Create opaque types that represent underlying Wayland types.
macro_rules! opaque {
    ($($(#[$meta:meta])* $name:ident),*) => {$(
        $(#[$meta])*
        #[allow(non_camel_case_types)]
        #[repr(C)]
        struct $name([usize; 0]);
    )*}
}

opaque! {
    wl_display,
    wl_event_queue,
    wl_proxy
}

/// Dispatcher function.
type DispatchFunction = unsafe extern "C" fn(
    user_data: *const c_void,
    proxy: *mut c_void,
    opcode: u32,
    message: *const Message,
    args: *const InnerArgument,
) -> c_int;

/// Wayland library.
struct Library {
    // TODO: Conditional dynamic dispatch
    _private: (),
}

impl Library {
    /// Load the Wayland library.
    // In the future we may add dynamic dispatching. So io::Result is justified.
    #[allow(clippy::unnecessary_wraps)]
    fn get() -> io::Result<&'static Self> {
        Ok(&Self { _private: () })
    }
}

/// Define Wayland methods.
macro_rules! methods {
    ($(
        $(#[$meta:meta])*
        fn $name:ident($($arg:ident: $argty:ty),* $(,)?) $(-> $ret:ty)?
    );* $(;)?) => {
        impl Library {
            $(
                $(#[$meta])*
                #[inline]
                pub unsafe fn $name(&self, $($arg: $argty),*) $(-> $ret)? {
                    #[link(name = "wayland-client")]
                    unsafe extern "C" {
                        fn $name($($arg: $argty),*) $(-> $ret)?;
                    }

                    unsafe {
                        $name($($arg),*)
                    }
                }
            )*
        }
    }
}

methods! {
    fn wl_display_cancel_read(display: *mut wl_display);
    fn wl_display_connect_to_fd(fd: OwnedFd) -> *mut wl_display;
    fn wl_display_create_queue(display: *mut wl_display) -> *mut wl_event_queue;
    fn wl_display_create_queue_with_name(display: *mut wl_display, name: *const c_char) -> *mut wl_event_queue;
    fn wl_display_disconnect(display: *mut wl_display);
    fn wl_display_dispatch_queue_pending(display: *mut wl_display, queue: *mut wl_event_queue) -> c_int;
    fn wl_display_get_fd(display: *mut wl_display) -> BorrowedFd<'static>;
    fn wl_display_flush(display: *mut wl_display) -> c_int;
    fn wl_display_prepare_read_queue(display: *mut wl_display, queue: *mut wl_event_queue) -> c_int;
    fn wl_display_read_events(display: *mut wl_display) -> c_int;

    fn wl_event_queue_destroy(queue: *mut wl_event_queue);

    fn wl_proxy_add_dispatcher(
        proxy: *mut wl_proxy,
        dispatch: Option<DispatchFunction>,
        implementation: *const c_void,
        data: *mut c_void,
    ) -> c_int;
    fn wl_proxy_get_id(proxy: *mut wl_proxy) -> u32;
    fn wl_proxy_get_listener(proxy: *mut wl_proxy) -> *mut c_void;
    fn wl_proxy_get_tag(proxy: *mut wl_proxy) -> *const c_char;
    fn wl_proxy_get_user_data(proxy: *mut wl_proxy) -> *mut c_void;
    fn wl_proxy_get_version(proxy: *mut wl_proxy) -> u32;
    fn wl_proxy_marshal_array(
        proxy: *mut wl_proxy,
        opcode: u32,
        args: *mut InnerArgument,
    );
    fn wl_proxy_marshal_array_constructor_versioned(
        source: *mut wl_proxy,
        opcode: u32,
        args: *mut InnerArgument,
        interface: *const Interface,
        version: u32,
    ) -> *mut wl_proxy;
    fn wl_proxy_set_tag(proxy: *mut wl_proxy, tag: *const c_char);
    fn wl_proxy_set_queue(proxy: *mut wl_proxy, queue: *mut wl_event_queue);
    fn wl_proxy_destroy(proxy: *mut wl_proxy);
}

//
// Rust API
//

std::thread_local! {
    static DISPATCHER_PANIC: Cell<Option<Box<dyn Any + Send>>> = Cell::new(None);
}

/// Connection to the Wayland server.
pub struct Display {
    /// Inner display.
    inner: Arc<InnerDisplay>,
}

/// Inner display.
struct InnerDisplay {
    /// Wayland function pointers.
    library: &'static Library,

    /// Pointer to the display.
    display: NonNull<wl_display>,

    /// Do we own the Wayland pointer?
    owned: bool,
}

unsafe impl Send for InnerDisplay {}
unsafe impl Sync for InnerDisplay {}

impl fmt::Debug for Display {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("Display")
            .field(&format_args!("{:p}", self.as_ptr()))
            .finish()
    }
}

impl Display {
    /// Connect to an existing socket.
    ///
    /// # Errors
    ///
    /// This function returns any errors that occur while `libwayland` is
    /// connecting to the socket.
    pub fn connect_to(socket: impl Into<OwnedFd>) -> io::Result<Self> {
        // Load the Wayland library.
        let library = Library::get()?;
        let socket = socket.into();

        // Open the Wayland connection.
        let connection = unsafe { library.wl_display_connect_to_fd(socket) };

        let connection = match NonNull::new(connection) {
            Some(connection) => connection,
            None => return Err(io::Error::last_os_error()),
        };

        Ok(Self {
            inner: Arc::new(InnerDisplay {
                library,
                display: connection,
                owned: true,
            }),
        })
    }

    /// Connect to the Wayland socket specified by environment variables.
    ///
    /// # Errors
    ///
    /// If there is an error connecting to the socket, this function returns
    /// the error. In addition, if any environment variables (like `WAYLAND_DISPLAY`,
    /// `XDG_RUNTIME_DIR`, or `WAYLAND_SOCKET`) are not set or populated
    /// incorrectly, this function returns an error.
    pub fn connect() -> io::Result<Self> {
        // In some cases we get the socket passed in via fork/dup.
        // Handle these cases by parsing WAYLAND_SOCKET.
        if let Ok(socket) = env::var("WAYLAND_SOCKET") {
            // Parse the socket.
            let socket = {
                let fd = socket.parse::<i32>().map_err(|_| {
                    io::Error::new(
                        io::ErrorKind::InvalidInput,
                        format!("WAYLAND_SOCKET is not a valid file descriptor: {}", &socket),
                    )
                })?;

                // SAFETY: We assume this is a valid file descriptor.
                unsafe { OwnedFd::from_raw_fd(fd) }
            };

            // Remove WAYLAND_SOCKET so child processes don't see it.
            unsafe {
                env::remove_var("WAYLAND_SOCKET");
            }

            // Set CLOEXEC on this socket.
            // TODO: We are importing rustix entirely for this one call.
            // We can probably get around this somehow with manual libc imports.
            // See if rustix pops up anywhere else in the dep tree.
            rio::fcntl_setfd(&socket, rio::fcntl_getfd(&socket)? | rio::FdFlags::CLOEXEC)?;
            return Self::connect_to(socket);
        }

        // Parse apart "WAYLAND_DISPLAY" for the socket's name.
        let mut socket_name: PathBuf = env::var_os("WAYLAND_DISPLAY")
            .ok_or_else(|| {
                io::Error::new(
                    io::ErrorKind::AddrNotAvailable,
                    "WAYLAND_DISPLAY is not set",
                )
            })?
            .into();

        // If the socket is not absolute, add XDG_RUNTIME_DIR to the front.
        if !socket_name.is_absolute() {
            let mut runtime_dir: PathBuf = env::var_os("XDG_RUNTIME_DIR")
                .ok_or_else(|| {
                    io::Error::new(
                        io::ErrorKind::AddrNotAvailable,
                        "XDG_RUNTIME_DIR is not set",
                    )
                })?
                .into();

            runtime_dir.push(socket_name);
            socket_name = runtime_dir;
        }

        // Connect to the socket.
        let socket = UnixStream::connect(&socket_name)
            .map_err(|err| io::Error::new(err.kind(), FileError::new(&socket_name, err)))?;
        Self::connect_to(socket)
    }

    /// Create a wrapper around an existing Wayland connection.
    ///
    /// # Errors
    ///
    /// This function will only return an error if there is an error while
    /// loading the underlying `Wayland` library.
    ///
    /// # Safety
    ///
    /// The pointer is assumed to be a valid Wayland display pointer. If `owned`
    /// is set, it is assumed we have permission to drop it.
    #[inline]
    pub unsafe fn from_ptr(display: *mut (), owned: bool) -> io::Result<Self> {
        // SAFETY: User asserts this pointer is valid, hence non-null.
        let display = unsafe { NonNull::new_unchecked(display.cast()) };

        Ok(Self {
            inner: Arc::new(InnerDisplay {
                library: Library::get()?,
                display,
                owned,
            }),
        })
    }

    /// Get the raw pointer to the display.
    #[must_use]
    #[inline]
    pub fn as_ptr(&self) -> *mut () {
        self.as_non_null().as_ptr()
    }

    /// Get a `NonNull` equivalent of the display.
    ///
    /// This is useful for cases that expect the non-null guarantee.
    #[must_use]
    #[inline]
    pub fn as_non_null(&self) -> NonNull<()> {
        self.inner.display.cast()
    }

    /// Flush the current display.
    ///
    /// # Errors
    ///
    /// This will try to flush the display over the I/O port and may error. If
    /// this error is `WouldBlock`, it's time to poll the file descriptor.
    #[inline]
    pub fn flush(&self) -> io::Result<()> {
        // SAFETY: We have a valid pointer.
        let ret = unsafe {
            self.inner
                .library
                .wl_display_flush(self.inner.display.as_ptr())
        };

        if ret < 0 {
            Err(io::Error::last_os_error())
        } else {
            Ok(())
        }
    }

    /// Create a new event queue with an optional name.
    ///
    /// # Errors
    ///
    /// This function returns any errors that occur while `libwayland` is
    /// creating the queue.
    #[inline]
    pub fn create_queue(&self, name: Option<&CStr>) -> io::Result<EventQueue> {
        // SAFETY: We have a valid pointer.
        let queue = match name {
            Some(name) => unsafe {
                self.inner
                    .library
                    .wl_display_create_queue_with_name(self.inner.display.as_ptr(), name.as_ptr())
            },
            None => unsafe {
                self.inner
                    .library
                    .wl_display_create_queue(self.inner.display.as_ptr())
            },
        };

        // Ensure it's non-null.
        let queue = match NonNull::new(queue) {
            Some(queue) => queue,
            None => return Err(io::Error::last_os_error()),
        };

        // SAFETY: This is a valid event queue.
        Ok(EventQueue {
            inner: Rc::new(InnerQueue {
                display: self.inner.clone(),
                queue,
            }),
        })
    }

    /// Begin reading from the [`Display`].
    ///
    /// # Errors
    ///
    /// This function returns any errors that occur while `libwayland` is
    /// preparing to read.
    #[inline]
    pub fn read<'this>(&'this self, queue: &'this mut EventQueue) -> io::Result<Read<'this>> {
        Read::new(self, queue)
    }

    /// Get the [`Proxy`] for the display object.
    #[inline]
    #[must_use]
    pub fn display_proxy(&self) -> protocol::wayland::wl_display::WlDisplay {
        // NOTE: Proxy code is hardcoded not to destroy the display.
        Proxy::borrowed(
            self.inner.library,
            self.inner.display.cast(),
            protocol::wayland::wl_display::WlDisplay::INTERFACE,
        )
        .into()
    }

    /// Dispatch all events.
    ///
    /// # Errors
    ///
    /// This function returns any errors that occur while `libwayland` is
    /// dispatching the events.
    #[inline]
    #[allow(clippy::single_match_else)]
    pub fn dispatch_queue(&self, queue: &mut EventQueue) -> io::Result<usize> {
        // SAFETY: We have a valid pointer.
        let ret = unsafe {
            self.inner.library.wl_display_dispatch_queue_pending(
                self.inner.display.as_ptr(),
                queue.inner.queue.as_ptr(),
            )
        };

        match usize::try_from(ret) {
            Ok(ret) => Ok(ret),
            Err(_) => {
                // See if there was a panic we need to resume.
                DISPATCHER_PANIC
                    .try_with(|last_panic| {
                        if let Some(panic) = last_panic.take() {
                            panic::resume_unwind(panic);
                        }
                    })
                    .ok();

                Err(io::Error::last_os_error())
            }
        }
    }
}

impl AsFd for Display {
    #[inline]
    fn as_fd(&self) -> BorrowedFd<'_> {
        // SAFETY: We have a valid pointer.
        unsafe {
            self.inner
                .library
                .wl_display_get_fd(self.inner.display.as_ptr())
        }
    }
}

impl AsRawFd for Display {
    #[inline]
    fn as_raw_fd(&self) -> RawFd {
        self.as_fd().as_raw_fd()
    }
}

impl Drop for InnerDisplay {
    #[inline]
    fn drop(&mut self) {
        if !self.owned {
            return;
        }

        // SAFETY: We have a valid pointer.
        unsafe {
            self.library.wl_display_disconnect(self.display.as_ptr());
        }
    }
}

#[cfg(feature = "raw-window-handle")]
impl raw_window_handle::HasDisplayHandle for Display {
    #[inline]
    fn display_handle(
        &self,
    ) -> Result<raw_window_handle::DisplayHandle<'_>, raw_window_handle::HandleError> {
        let handle = raw_window_handle::WaylandDisplayHandle::new(self.as_non_null().cast());
        Ok(unsafe { raw_window_handle::DisplayHandle::borrow_raw(handle.into()) })
    }
}

// SAFETY: Wayland's display logic is locked behind a Mutex.
unsafe impl Send for Display {}
// SAFETY: Ditto
unsafe impl Sync for Display {}

/// A queue of Wayland events.
pub struct EventQueue {
    /// Inner event queue.
    inner: Rc<InnerQueue>,
}

/// Inner implementation of the queue.
struct InnerQueue {
    /// Back-reference to the display.
    display: Arc<InnerDisplay>,

    /// Pointer to the event queue.
    queue: NonNull<wl_event_queue>,
}

impl fmt::Debug for EventQueue {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("EventQueue")
            .field(&format_args!("{:p}", self.inner.queue))
            .finish()
    }
}

impl Drop for InnerQueue {
    #[inline]
    fn drop(&mut self) {
        // SAFETY: We have a valid pointer.
        unsafe {
            self.display
                .library
                .wl_event_queue_destroy(self.queue.as_ptr());
        }
    }
}

// Note: wl_event_queue is technically send, but making it !Send
// lets us avoid needing to do atomic refcounting.

/// Read guard for reading from a [`Display`].
#[derive(Debug)]
pub struct Read<'a> {
    /// Underlying display.
    display: &'a Display,

    /// Event queue to read into.
    event_queue: &'a mut EventQueue,

    /// Have we finished reading events?
    finished: bool,
}

impl<'a> Read<'a> {
    /// Prepare for an event read.
    #[inline]
    fn new(display: &'a Display, event_queue: &'a mut EventQueue) -> io::Result<Self> {
        let ret = unsafe {
            display.inner.library.wl_display_prepare_read_queue(
                display.inner.display.as_ptr(),
                event_queue.inner.queue.as_ptr(),
            )
        };

        if ret < 0 {
            return Err(io::Error::last_os_error());
        }

        Ok(Self {
            display,
            event_queue,
            finished: false,
        })
    }

    /// Read events from the display and dispatch them.
    ///
    /// # Errors
    ///
    /// This function returns any errors that occur while `libwayland` is
    /// reading the events.
    #[inline]
    pub fn read(mut self) -> io::Result<usize> {
        self.read_without_dispatch()?;
        self.display.dispatch_queue(self.event_queue)
    }

    /// Read events from the display without dispatching them.
    ///
    /// # Errors
    ///
    /// This function returns any errors that occur while `libwayland` is
    /// reading the events.
    #[inline]
    pub fn read_without_dispatch(&mut self) -> io::Result<()> {
        // We've read events, so we don't need to cancel.
        self.finished = true;

        // SAFETY: We have a valid pointer.
        let ret = unsafe {
            self.display
                .inner
                .library
                .wl_display_read_events(self.display.inner.display.as_ptr())
        };

        if ret < 0 {
            return Err(io::Error::last_os_error());
        }

        Ok(())
    }
}

impl AsFd for Read<'_> {
    #[inline]
    fn as_fd(&self) -> BorrowedFd<'_> {
        self.display.as_fd()
    }
}

impl AsRawFd for Read<'_> {
    #[inline]
    fn as_raw_fd(&self) -> RawFd {
        self.display.as_raw_fd()
    }
}

impl Drop for Read<'_> {
    #[inline]
    fn drop(&mut self) {
        if self.finished {
            return;
        }

        // SAFETY: We have a valid pointer.
        unsafe {
            self.display
                .inner
                .library
                .wl_display_cancel_read(self.display.inner.display.as_ptr());
        }
    }
}

/// A builder for a Wayland proxy.
pub struct Proxy {
    /// Wayland library.
    library: &'static Library,

    /// Proxy type.
    ty: ProxyType,

    /// Pointer to the proxy.
    ///
    /// As a side-effect, this marks the structure as `!Send` and `!Sync`.
    /// This is left intentionally, even though proxies are partially thread-safe.
    /// That way we can use `F` with only `'static` as a restriction.
    ptr: NonNull<wl_proxy>,

    /// Proxy interface.
    interface: &'static Interface,
}

/// Type of proxy.
enum ProxyType {
    /// Owned proxy.
    Owned {
        /// Keep the event queue alive.
        _event_queue: Rc<InnerQueue>,
    },

    /// Borrowed proxy.
    Borrowed,
}

impl Proxy {
    /// Create a new proxy from an existing pointer.
    fn new(
        library: &'static Library,
        proxy: NonNull<wl_proxy>,
        interface: &'static Interface,
        event_queue: &EventQueue,
    ) -> Self {
        // Set the event queue.
        unsafe {
            library.wl_proxy_set_queue(proxy.as_ptr(), event_queue.inner.queue.as_ptr());
        }

        Self {
            library,
            ty: ProxyType::Owned {
                _event_queue: event_queue.inner.clone(),
            },
            ptr: proxy,
            interface,
        }
    }

    /// Borrowed proxy.
    fn borrowed(
        library: &'static Library,
        proxy: NonNull<wl_proxy>,
        interface: &'static Interface,
    ) -> Self {
        Self {
            library,
            ty: ProxyType::Borrowed,
            ptr: proxy,
            interface,
        }
    }

    /// Get the raw pointer to the proxy.
    #[must_use]
    #[inline]
    pub fn as_ptr(&self) -> *mut () {
        self.as_non_null().as_ptr()
    }

    /// Get a `NonNull` equivalent of the proxy.
    ///
    /// This is useful for cases that expect the non-null guarantee.
    #[must_use]
    #[inline]
    pub fn as_non_null(&self) -> NonNull<()> {
        self.ptr.cast()
    }

    /// Get the ID of this proxy.
    #[must_use]
    #[inline]
    pub fn id(&self) -> u32 {
        // SAFETY: We have a valid pointer.
        unsafe { self.library.wl_proxy_get_id(self.ptr.as_ptr()) }
    }

    /// Get the version of this proxy.
    #[must_use]
    #[inline]
    pub fn version(&self) -> u32 {
        // SAFETY: We have a valid pointer.
        unsafe { self.library.wl_proxy_get_version(self.ptr.as_ptr()) }
    }

    /// Get the interface for this proxy.
    #[must_use]
    #[inline]
    pub fn interface(&self) -> &'static Interface {
        self.interface
    }

    /// Get the tag for this proxy.
    #[must_use]
    #[inline]
    pub fn tag(&self) -> Option<&'static CStr> {
        // SAFETY: We have a valid pointer.
        let ptr = unsafe { self.library.wl_proxy_get_tag(self.ptr.as_ptr()) };
        if ptr.is_null() {
            None
        } else {
            Some(unsafe { CStr::from_ptr(ptr) })
        }
    }

    /// Set the tag for this proxy.
    #[inline]
    pub fn set_tag(&self, tag: &'static CStr) {
        // SAFETY: We have a valid pointer.
        unsafe {
            self.library
                .wl_proxy_set_tag(self.ptr.as_ptr(), tag.as_ptr());
        }
    }

    /// Send a request to the Wayland compositor.
    ///
    /// # Safety
    ///
    /// The arguments for this call are not checked. Ensure that the arguments
    /// match the actual message signature.
    #[inline]
    pub unsafe fn send_message_unchecked(&self, opcode: u32, message: &[Argument<'_, '_>]) {
        // SAFETY: We have a valid pointer, args are checked by the user.
        unsafe {
            self.library.wl_proxy_marshal_array(
                self.ptr.as_ptr(),
                opcode,
                message.as_ptr().cast_mut().cast(),
            );
        }
    }

    /// Send a message, checking the arguments prior to sending.
    ///
    /// # Errors
    ///
    /// If the arguments do not match the message signature, this function
    /// returns an error.
    #[inline]
    pub fn send_message(&self, opcode: u32, args: &ArgumentBuffer<'_, '_, '_>) -> io::Result<()> {
        self.check_message(opcode, args)?;

        // SAFETY: Everything is checked.
        unsafe {
            self.send_message_unchecked(opcode, args.args);
        }

        Ok(())
    }

    /// Send a request to the Wayland compositor that returns a new proxy.
    ///
    /// # Safety
    ///
    /// The arguments for this call are not checked. Ensure that the arguments
    /// match the actual message signature.
    ///
    /// # Errors
    ///
    /// If the arguments do not match the message signature, this function
    /// returns an error.
    #[inline]
    pub unsafe fn send_message_constructor_unchecked(
        &self,
        opcode: u32,
        args: &[Argument<'_, '_>],
        interface: &'static Interface,
        version: u32,
        event_queue: &EventQueue,
    ) -> io::Result<Proxy> {
        // SAFETY: We have a valid pointer, args are checked by the user.
        let proxy = unsafe {
            self.library.wl_proxy_marshal_array_constructor_versioned(
                self.ptr.as_ptr(),
                opcode,
                args.as_ptr().cast_mut().cast(),
                interface,
                version,
            )
        };

        // Ensure it's non-null.
        let proxy = match NonNull::new(proxy) {
            Some(proxy) => proxy,
            None => return Err(io::Error::last_os_error()),
        };

        Ok(Proxy::new(self.library, proxy, interface, event_queue))
    }

    /// Send a request to the Wayland compositor that returns a new proxy.
    ///
    /// # Errors
    ///
    /// If the arguments do not match the message signature, this function
    /// returns an error.
    #[inline]
    pub fn send_message_constructor(
        &self,
        opcode: u32,
        args: &ArgumentBuffer<'_, '_, '_>,
        interface: &'static Interface,
        version: u32,
        event_queue: &EventQueue,
    ) -> io::Result<Proxy> {
        self.check_message(opcode, args)?;

        // SAFETY: Everything is checked.
        unsafe {
            self.send_message_constructor_unchecked(
                opcode,
                args.args,
                interface,
                version,
                event_queue,
            )
        }
    }

    /// Add a listener to this proxy.
    ///
    /// # Errors
    ///
    /// Returns an error if a listener is already set.
    #[inline]
    #[allow(clippy::ptr_as_ptr)]
    pub fn add_listener<F: FnMut(Proxy, u32, IncomingArguments<'_, '_, '_>) + 'static>(
        &self,
        listener: F,
    ) -> io::Result<()> {
        // Can only be done for owned proxies.
        if !matches!(self.ty, ProxyType::Owned { .. }) {
            return Err(io::Error::new(
                io::ErrorKind::InvalidInput,
                "can only add a listener to an owned proxy",
            ));
        }

        // Create the user data.
        let userdata = Box::into_raw(Box::new(Userdata {
            listener,
            interface: self.interface,
        }));

        // SAFETY: We have a valid pointer.
        let result = unsafe {
            self.library.wl_proxy_add_dispatcher(
                self.ptr.as_ptr(),
                Some(Userdata::<F>::dispatcher),
                std::ptr::from_ref(Userdata::<F>::vtable()) as *const _,
                userdata as *mut c_void,
            )
        };

        if result < 0 {
            drop(unsafe { Box::from_raw(userdata) });
            return Err(io::Error::last_os_error());
        }

        Ok(())
    }

    /// Check message and argument types.
    #[inline]
    fn check_message(&self, opcode: u32, args: &ArgumentBuffer<'_, '_, '_>) -> io::Result<()> {
        // Get the message that this opcode corresponds to.
        let message = self
            .interface
            .methods()
            .get(opcode as usize)
            .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidInput, "opcode out of range"))?;

        // Check every argument type.
        for (expected, found) in message.signature().iter().zip(args.types.iter()) {
            if expected != *found {
                return Err(io::Error::new(
                    io::ErrorKind::InvalidInput,
                    format!("expected argument of type {expected:?}, found {found:?}"),
                ));
            }
        }

        Ok(())
    }

    /// Drop the inner listener.
    #[inline]
    fn drop_listener(&self) -> bool {
        let listener = unsafe { self.library.wl_proxy_get_listener(self.ptr.as_ptr()) };
        if listener.is_null() {
            return false;
        }

        // Read the pointer and check the magic.
        let magic = unsafe { (listener as *const u64).read() };
        if magic != MAGIC {
            // Not our listener.
            return false;
        }

        // Read out the userdata and drop it.
        let userdata = unsafe { self.library.wl_proxy_get_user_data(self.ptr.as_ptr()) };
        let vtable = unsafe { &*(listener as *const ProxyVTable) };
        unsafe {
            (vtable.dropper)(userdata.cast());
        }

        true
    }
}

#[cfg(feature = "raw-window-handle")]
impl raw_window_handle::HasWindowHandle for protocol::wayland::WlSurface {
    #[inline]
    fn window_handle(
        &self,
    ) -> Result<raw_window_handle::WindowHandle<'_>, raw_window_handle::HandleError> {
        let handle =
            raw_window_handle::WaylandWindowHandle::new(self.as_ref().as_non_null().cast());
        Ok(unsafe { raw_window_handle::WindowHandle::borrow_raw(handle.into()) })
    }
}

impl fmt::Debug for Proxy {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("Proxy")
            .field(&format_args!("{:p}", self.ptr))
            .finish()
    }
}

impl Drop for Proxy {
    #[inline]
    fn drop(&mut self) {
        if matches!(self.ty, ProxyType::Borrowed) {
            return;
        }

        // Take out the listener if we are rust managed.
        self.drop_listener();

        if self.id() == 1 {
            // Can't drop the display.
            return;
        }

        // SAFETY: We have a valid pointer.
        unsafe {
            self.library.wl_proxy_destroy(self.ptr.as_ptr());
        }
    }
}

/// User-data for a proxy.
struct Userdata<F> {
    /// Listener callback.
    listener: F,

    /// Interface.
    interface: &'static Interface,
}

const MAGIC: u64 = (4u64 * 8 * 15 * 16 * 23 * 42).wrapping_pow(0x3) ^ u64::MAX;

impl<F> Userdata<F>
where
    F: FnMut(Proxy, u32, IncomingArguments<'_, '_, '_>) + 'static,
{
    /// Get the vtable for this userdata.
    fn vtable() -> &'static ProxyVTable {
        &ProxyVTable {
            magic: MAGIC,
            dropper: Self::drop_userdata,
        }
    }

    /// Dispatcher function passed to libwayland.
    unsafe extern "C" fn dispatcher(
        _: *const c_void,
        proxy: *mut c_void,
        opcode: u32,
        message: *const Message,
        args: *const InnerArgument,
    ) -> c_int {
        // Abort on panic.
        struct Bomb;

        impl Drop for Bomb {
            #[cold]
            fn drop(&mut self) {
                std::process::abort();
            }
        }

        // Intercept any panics that may occur.
        let panic = panic::catch_unwind(panic::AssertUnwindSafe(move || {
            // Get the library.
            let library = Library::get().unwrap();

            // Cast down userdata.
            let userdata_ptr = unsafe { library.wl_proxy_get_user_data(proxy.cast()) };
            let userdata: &mut Userdata<F> = unsafe { &mut *(userdata_ptr.cast::<Userdata<F>>()) };

            // Create a shim proxy object.
            let proxy = Proxy::borrowed(
                library,
                unsafe { NonNull::new_unchecked(proxy.cast()) },
                userdata.interface,
            );

            // Call the listener.
            let incoming = unsafe { IncomingArguments::new(&*message, args.cast()) };
            (userdata.listener)(proxy, opcode, incoming);
        }));

        if let Err(panic) = panic {
            // Stash the panic in the thread local.
            // Use an abort-on-panic guard on the off chance that the thread
            // local somehow panics.
            let bomb = Bomb;
            DISPATCHER_PANIC
                .try_with(|last_panic| {
                    last_panic.set(Some(panic));
                })
                .ok();
            mem::forget(bomb);
        }

        0
    }

    /// Drop the userdata.
    unsafe fn drop_userdata(userdata: *mut ()) {
        drop(unsafe { Box::from_raw(userdata.cast::<Userdata<F>>()) });
    }
}

/// Virtual Function Table for a Proxy
#[repr(C)]
struct ProxyVTable {
    /// Magic number.
    ///
    /// This is used to assert that the v-table is owned by us.
    magic: u64,

    /// Drop the heap allocation.
    dropper: unsafe fn(*mut ()),
}

//
// Wayland Protocol Interfaces
//

/// Interface for a Wayland object.
#[repr(C)]
#[allow(non_camel_case_types)]
pub struct Interface {
    // Layout-compatible with wl_interface
    name: NonNull<c_char>,
    version: c_int,
    method_count: c_int,
    methods: *mut Message,
    event_count: c_int,
    events: *mut Message,
}

unsafe impl Send for Interface {}
unsafe impl Sync for Interface {}

impl Interface {
    /// Create a new interface.
    #[must_use]
    #[allow(clippy::cast_possible_truncation, clippy::cast_possible_wrap)]
    pub const fn new(
        name: &'static CStr,
        version: i32,
        methods: &'static [Message],
        events: &'static [Message],
    ) -> Self {
        assert!(methods.len() <= c_int::MAX as usize);
        assert!(events.len() <= c_int::MAX as usize);

        Self {
            name: unsafe { NonNull::new_unchecked(name.as_ptr().cast_mut()) },
            version,
            method_count: methods.len() as c_int,
            methods: methods.as_ptr().cast_mut(),
            event_count: events.len() as c_int,
            events: events.as_ptr().cast_mut(),
        }
    }

    /// Get the name of the interface.
    #[must_use]
    pub fn name(&self) -> &'static CStr {
        unsafe { CStr::from_ptr(self.name.as_ptr()) }
    }

    /// Get the version of the interface.
    #[must_use]
    pub fn version(&self) -> i32 {
        self.version
    }

    /// Get the methods of the interface.
    #[must_use]
    #[allow(clippy::cast_sign_loss)]
    pub fn methods(&self) -> &'static [Message] {
        unsafe { std::slice::from_raw_parts(self.methods, self.method_count as usize) }
    }

    /// Get the events of the interface.
    #[must_use]
    #[allow(clippy::cast_sign_loss)]
    pub fn events(&self) -> &'static [Message] {
        unsafe { std::slice::from_raw_parts(self.events, self.event_count as usize) }
    }
}

impl fmt::Debug for Interface {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Interface")
            .field("name", &self.name())
            .field("version", &self.version())
            .field("methods", &self.methods())
            .field("events", &self.events())
            .finish()
    }
}

/// Description of a Wayland message.
#[repr(C)]
#[allow(non_camel_case_types)]
pub struct Message {
    // Layout-compatible with wl_message
    name: NonNull<c_char>,
    signature: NonNull<c_char>,
    types: *mut *mut Interface,
}

unsafe impl Send for Message {}
unsafe impl Sync for Message {}

impl Message {
    /// Create a new [`Message`].
    #[must_use]
    pub const fn new(
        name: &'static CStr,
        signature: &'static Signature,
        types: &'static [Option<&'static Interface>],
    ) -> Self {
        assert!(signature.len() == types.len());

        Self {
            name: unsafe { NonNull::new_unchecked(name.as_ptr().cast_mut()) },
            signature: unsafe { NonNull::new_unchecked(signature.0.as_ptr().cast_mut()) },
            types: types.as_ptr().cast::<*mut Interface>().cast_mut(),
        }
    }

    /// Get the name of the message.
    #[must_use]
    pub fn name(&self) -> &'static CStr {
        unsafe { CStr::from_ptr(self.name.as_ptr()) }
    }

    /// Get the signature of the message.
    #[must_use]
    #[allow(clippy::missing_panics_doc)]
    pub fn signature(&self) -> &'static Signature {
        unsafe { Signature::from_ptr(self.signature.as_ptr()) }
    }

    /// Get the types of the message.
    #[must_use]
    pub fn types(&self) -> &'static [Option<&'static Interface>] {
        unsafe { std::slice::from_raw_parts(self.types.cast(), self.signature().len()) }
    }
}

impl fmt::Debug for Message {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_struct("Message")
            .field("name", &self.name())
            .field("signature", &self.signature())
            .field("types", &self.types())
            .finish()
    }
}

/// Signature for a message.
#[repr(transparent)]
pub struct Signature(CStr);

impl Signature {
    /// Create a new signature.
    ///
    /// Returns `None` if the signature is invalid.
    #[must_use]
    pub const fn new(signature: &CStr) -> Option<&Self> {
        let bytes = signature.to_bytes();
        let mut i = 0;

        // Ensure all characters are accounted for.
        while i < bytes.len() {
            match bytes[i] {
                b'i' | b'u' | b'f' | b's' | b'o' | b'n' | b'a' | b'h' => {}
                b'?' => {
                    // Read the next character.
                    i += 1;
                    if i >= bytes.len() {
                        return None;
                    }
                    match bytes[i] {
                        b'o' | b's' => {}
                        _ => return None,
                    }
                }
                _ => return None,
            }

            i += 1;
        }

        Some(unsafe { Self::from_cstr_unchecked(signature) })
    }

    /// Get a signature from an unchecked pointer.
    ///
    /// # Safety
    ///
    /// `ptr` must be a valid Wayland signature.
    #[must_use]
    pub const unsafe fn from_ptr<'a>(ptr: *const c_char) -> &'a Self {
        let cstr = unsafe { CStr::from_ptr(ptr) };
        unsafe { Self::from_cstr_unchecked(cstr) }
    }

    /// Get a signature from a `CStr` without checking.
    ///
    /// # Safety
    ///
    /// `signature` must be a valid Wayland signature.
    #[must_use]
    pub const unsafe fn from_cstr_unchecked(signature: &CStr) -> &Self {
        unsafe { &*(ptr::from_ref::<CStr>(signature) as *const Self) }
    }

    /// Get the number of types in this signature.
    #[allow(clippy::missing_panics_doc, clippy::len_without_is_empty)]
    #[must_use]
    pub const fn len(&self) -> usize {
        let mut count = 0;
        let mut i = 0;
        let bytes = self.0.to_bytes();

        while i < bytes.len() {
            match bytes[i] {
                b'i' | b'u' | b'f' | b's' | b'o' | b'n' | b'a' | b'h' => count += 1,
                b'?' => {
                    i += 1;
                    count += 1;
                }
                _ => unreachable!(),
            }
            i += 1;
        }

        count
    }

    /// Iterate over signature elements.
    #[must_use]
    pub fn iter(&self) -> SignatureIter<'_> {
        SignatureIter {
            inner: self.0.to_bytes().iter(),
        }
    }
}

impl<'a> IntoIterator for &'a Signature {
    type Item = ArgumentType;
    type IntoIter = SignatureIter<'a>;

    #[inline]
    fn into_iter(self) -> Self::IntoIter {
        self.iter()
    }
}

impl fmt::Debug for Signature {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("Signature").field(&&self.0).finish()
    }
}

/// Iterate over the types in a signature.
pub struct SignatureIter<'a> {
    /// Inner iterator.
    inner: slice::Iter<'a, u8>,
}

impl fmt::Debug for SignatureIter<'_> {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("SignatureIter")
            .field(&self.inner.as_slice())
            .finish()
    }
}

impl Iterator for SignatureIter<'_> {
    type Item = ArgumentType;

    #[inline]
    fn next(&mut self) -> Option<Self::Item> {
        self.inner.next().map(|&c| match c {
            b'i' => ArgumentType::Int32,
            b'u' => ArgumentType::UInt32,
            b'f' => ArgumentType::Float,
            b's' => ArgumentType::String { nullable: false },
            b'o' => ArgumentType::Object { nullable: false },
            b'n' => ArgumentType::NewId,
            b'a' => ArgumentType::Array,
            b'h' => ArgumentType::FileDescriptor,
            b'?' => match self.inner.next().unwrap() {
                b's' => ArgumentType::String { nullable: true },
                b'o' => ArgumentType::Object { nullable: true },
                _ => unreachable!(),
            },
            _ => unreachable!(),
        })
    }

    #[inline]
    fn size_hint(&self) -> (usize, Option<usize>) {
        let (lo, hi) = self.inner.size_hint();
        (lo / 2, hi)
    }
}

/// The type that an [`Argument`] can have.
#[derive(Debug, Copy, Clone, PartialEq, Eq)]
#[non_exhaustive]
pub enum ArgumentType {
    /// 32-bit signed integer.
    Int32,

    /// 32-bit unsigned integer.
    UInt32,

    /// 32-bit floating point number.
    Float,

    /// String.
    String {
        /// Whether the string is nullable.
        nullable: bool,
    },

    /// Object.
    Object {
        /// Whether the object is nullable.
        nullable: bool,
    },

    /// New ID.
    NewId,

    /// Array.
    Array,

    /// File descriptor.
    FileDescriptor,
}

/// Typed [`Argument`].
#[derive(Debug)]
#[non_exhaustive]
pub enum TypedArgument<'arg, 'dat, NewId> {
    /// 32-bit signed integer.
    Int32(i32),

    /// 32-bit unsigned integer.
    UInt32(u32),

    /// 32-bit floating point number.
    Float(Fixed),

    /// String.
    String(Option<&'arg CStr>),

    /// Object.
    Object(Option<Proxy>),

    /// New ID.
    NewId(NewId),

    /// Array.
    Array(&'arg Array<'dat>),

    /// File descriptor.
    FileDescriptor(BorrowedFd<'arg>),
}

impl<'arg, 'dat> TypedArgument<'arg, 'dat, ()> {
    /// Create a new typed argument without new-id proxy.
    ///
    /// # Safety
    ///
    /// `arg` must be a valid argument for `ty`.
    #[inline]
    #[must_use]
    pub unsafe fn with_no_proxy(
        existing_proxy: &Proxy,
        arg: Argument<'arg, 'dat>,
        ty: ArgumentType,
    ) -> TypedArgument<'arg, 'dat, ()> {
        unsafe { Self::new(existing_proxy, arg, ty, |_| ()) }
    }
}

impl<'arg, 'dat> TypedArgument<'arg, 'dat, Option<Proxy>> {
    /// Create a new typed argument.
    ///
    /// # Safety
    ///
    /// `arg` must be a valid argument for `ty`.
    #[inline]
    #[must_use]
    pub unsafe fn with_proxy(
        existing_proxy: &Proxy,
        arg: Argument<'arg, 'dat>,
        ty: ArgumentType,
    ) -> TypedArgument<'arg, 'dat, Option<Proxy>> {
        unsafe {
            Self::new(existing_proxy, arg, ty, |new_id| {
                NonNull::new(new_id.cast())
                    .map(|p| Proxy::borrowed(existing_proxy.library, p, existing_proxy.interface))
            })
        }
    }
}

impl<'arg, 'dat, NewId> TypedArgument<'arg, 'dat, NewId> {
    /// Create a new typed argument.
    unsafe fn new<Cvt: Fn(*mut ()) -> NewId>(
        existing_proxy: &Proxy,
        arg: Argument<'arg, 'dat>,
        ty: ArgumentType,
        cvt: Cvt,
    ) -> Self {
        match ty {
            ArgumentType::Int32 => TypedArgument::Int32(unsafe { arg.arg.i }),
            ArgumentType::UInt32 => TypedArgument::UInt32(unsafe { arg.arg.u }),
            ArgumentType::Float => TypedArgument::Float(Fixed::from_raw(unsafe { arg.arg.f })),
            ArgumentType::String { nullable: true } => TypedArgument::String(unsafe {
                if arg.arg.s.is_null() {
                    None
                } else {
                    Some(CStr::from_ptr(arg.arg.s))
                }
            }),
            ArgumentType::String { nullable: false } => {
                TypedArgument::String(unsafe { Some(CStr::from_ptr(arg.arg.s)) })
            }
            ArgumentType::Object { .. } => TypedArgument::Object(unsafe {
                NonNull::new(arg.arg.o.cast())
                    .map(|p| Proxy::borrowed(existing_proxy.library, p, existing_proxy.interface))
            }),
            ArgumentType::NewId => TypedArgument::NewId(cvt(unsafe { arg.arg.o.cast() })),
            ArgumentType::Array => TypedArgument::Array(unsafe { &*arg.arg.a.cast() }),
            ArgumentType::FileDescriptor => {
                TypedArgument::FileDescriptor(unsafe { BorrowedFd::borrow_raw(arg.arg.h) })
            }
        }
    }
}

/// Buffer for arguments and buffer for argument types.
pub struct ArgumentBuffer<'list, 'arg, 'dat> {
    /// List of arguments.
    args: &'list [Argument<'arg, 'dat>],

    /// List of argument types.
    types: &'list [ArgumentType],
}

impl<'list, 'arg, 'dat> ArgumentBuffer<'list, 'arg, 'dat> {
    /// Create a new `ArgumentBuffer`.
    ///
    /// # Safety
    ///
    /// `types` must appropriately describe `args`.
    #[must_use]
    #[inline]
    unsafe fn __new(args: &'list [Argument<'arg, 'dat>], types: &'list [ArgumentType]) -> Self {
        assert_eq!(args.len(), types.len());

        Self { args, types }
    }

    /// Iterate over arguments in this buffer.
    #[inline]
    pub fn iter<'x>(
        &'x self,
        existing_proxy: &'x Proxy,
    ) -> impl Iterator<Item = TypedArgument<'arg, 'dat, ()>> + 'x {
        self.args
            .iter()
            .zip(self.types.iter())
            .map(|(arg, ty)| unsafe { TypedArgument::with_no_proxy(existing_proxy, *arg, *ty) })
    }
}

impl fmt::Debug for ArgumentBuffer<'_, '_, '_> {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("ArgumentBuffer")
            .field(&format_args!(".."))
            .finish()
    }
}

/// Create an [`ArgumentBuffer`] safely.
#[doc(hidden)]
macro_rules! args {
    // Rule to expand the type.
    (@t Int32) => {{ $crate::ArgumentType::Int32 }};
    (@t UInt32) => {{ $crate::ArgumentType::UInt32 }};
    (@t Float) => {{ $crate::ArgumentType::Float }};
    (@t String) => {{ $crate::ArgumentType::String { nullable: false } }};
    (@t NString) => {{ $crate::ArgumentType::String { nullable: true } }};
    (@t Object) => {{ $crate::ArgumentType::Object { nullable: false } }};
    (@t NObject) => {{ $crate::ArgumentType::Object { nullable: true } }};
    (@t NewId) => {{ $crate::ArgumentType::NewId }};
    (@t Array) => {{ $crate::ArgumentType::Array }};
    (@t FileDescriptor) => {{ $crate::ArgumentType::FileDescriptor }};

    // Rule to expand the argument.
    (@a Int32, $x:expr) => {{ $crate::Argument::from_i32($x) }};
    (@a UInt32, $x:expr) => {{ $crate::Argument::from_u32($x) }};
    (@a Float, $x:expr) => {{ $crate::Argument::from_f32($x) }};
    (@a String, $x:expr) => {{ $crate::Argument::from_str($x) }};
    (@a NString, $x:expr) => {{ $crate::Argument::from_str($x) }};
    (@a Object, $x:expr) => {{ $crate::Argument::from_proxy($x) }};
    (@a NObject, $x:expr) => {{ $crate::Argument::from_proxy($x) }};
    (@a NewId, $x:expr) => {{ $crate::Argument::new_id() }};
    (@a Array, $x:expr) => {{ $crate::Argument::from_array($x) }};
    (@a FileDescriptor, $x:expr) => {{ $crate::Argument::from_fd($x) }};

    // Rule to expand the argument buffer.
    ($a:ident = $(($name:ident, $expr:expr)),* $(,)?) => {
        let __args = &[
            $(
                $crate::args!(@a $name, $expr),
            )*
        ];
        let $a = unsafe {
            $crate::ArgumentBuffer::__new(
                __args.as_slice(),
                &[
                    $(
                        $crate::args!(@t $name),
                    )*
                ],
            )
        };
    }
}

use args;

/// Iterator over incoming event arguments.
pub struct IncomingArguments<'msg, 'arg, 'dat> {
    /// Message signature.
    message: &'msg Message,

    /// Incoming arguments.
    args: *const Argument<'arg, 'dat>,
}

impl<'msg, 'arg, 'dat> IncomingArguments<'msg, 'arg, 'dat> {
    /// Create the iterator.
    unsafe fn new(message: &'msg Message, args: *const Argument<'arg, 'dat>) -> Self {
        Self { message, args }
    }

    /// Get the message signature.
    #[must_use]
    pub fn signature(&self) -> &'msg Signature {
        self.message.signature()
    }

    /// Get the pointer to the incoming arguments.
    #[must_use]
    pub fn as_ptr(&self) -> *const Argument<'arg, 'dat> {
        self.args
    }

    /// Iterate over arguments.
    pub fn iter<'x>(
        &'x self,
        existing_proxy: &'x Proxy,
    ) -> impl Iterator<Item = TypedArgument<'arg, 'dat, Option<Proxy>>> + 'x {
        let mut ptr = self.as_ptr();
        self.signature().iter().map(move |ty| unsafe {
            let arg = ptr.read();
            ptr = ptr.add(1);
            TypedArgument::with_proxy(existing_proxy, arg, ty)
        })
    }
}

impl fmt::Debug for IncomingArguments<'_, '_, '_> {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("IncomingArguments")
            .field(&format_args!(".."))
            .finish()
    }
}

/// Fixed-size integer.
///
/// Wayland floats are conveyed as fixed-size integers, with one sign bit,
/// 23 bits of integer precision and 8 bits of fractional precision.
#[derive(Debug, Copy, Clone, PartialEq, Eq)]
pub struct Fixed(i32);

impl Fixed {
    /// Create a fixed-size integer from a raw value.
    #[must_use]
    #[inline]
    pub const fn from_raw(value: i32) -> Self {
        Self(value)
    }

    /// Get the raw value of the fixed-size integer.
    #[must_use]
    #[inline]
    pub const fn to_raw(self) -> i32 {
        self.0
    }

    /// Create a fixed-size decimal from a floating point value.
    #[must_use]
    #[inline]
    #[allow(clippy::float_arithmetic, clippy::cast_possible_truncation)]
    pub fn from_f32(value: f64) -> Self {
        Self((value * 256.0) as i32)
    }

    /// Get the floating point value of the fixed-size decimal.
    #[must_use]
    #[inline]
    #[allow(clippy::float_arithmetic, clippy::cast_precision_loss)]
    pub fn to_f32(self) -> f64 {
        f64::from(self.0) / 256.0
    }
}

impl From<f64> for Fixed {
    #[inline]
    fn from(value: f64) -> Self {
        Self::from_f32(value)
    }
}

impl From<Fixed> for f64 {
    #[inline]
    fn from(value: Fixed) -> Self {
        value.to_f32()
    }
}

//
// Arguments
//

/// An argument for a Wayland function.
#[repr(transparent)]
#[derive(Clone, Copy)]
pub struct Argument<'a, 'dat> {
    arg: InnerArgument,
    _marker: PhantomData<&'a Array<'dat>>,
}

impl fmt::Debug for Argument<'_, '_> {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("Argument")
            .field(&format_args!(".."))
            .finish()
    }
}

/// Inner representation of an argument.
#[repr(C)]
#[derive(Clone, Copy)]
union InnerArgument {
    i: i32,
    u: u32,
    f: i32,
    s: *const c_char,
    o: *mut wl_proxy,
    n: u32,
    a: *mut InnerArray,
    h: RawFd,
}

impl Argument<'static, 'static> {
    /// Create a new argument from an integer.
    #[must_use]
    pub fn from_i32(value: i32) -> Self {
        Self {
            arg: InnerArgument { i: value },
            _marker: PhantomData,
        }
    }

    /// Create a new argument from an unsigned integer.
    #[must_use]
    pub fn from_u32(value: u32) -> Self {
        Self {
            arg: InnerArgument { u: value },
            _marker: PhantomData,
        }
    }

    /// Create an argument for a new ID.
    #[must_use]
    pub fn new_id() -> Self {
        Self {
            arg: InnerArgument { n: 0 },
            _marker: PhantomData,
        }
    }

    /// Create an argument from a fixed point number.
    #[must_use]
    pub fn from_fixed(value: Fixed) -> Self {
        Self {
            arg: InnerArgument { f: value.0 },
            _marker: PhantomData,
        }
    }
}

impl<'a> Argument<'a, 'static> {
    /// Create a new argument from a string.
    #[must_use]
    #[allow(clippy::should_implement_trait)]
    pub fn from_str(value: Option<&'a CStr>) -> Self {
        Self {
            arg: InnerArgument {
                s: value.map_or(ptr::null(), CStr::as_ptr),
            },
            _marker: PhantomData,
        }
    }

    /// Create a new argument from a proxy.
    #[must_use]
    pub fn from_proxy(value: Option<&'a Proxy>) -> Self {
        Self {
            arg: InnerArgument {
                o: value.map_or(ptr::null_mut(), |value| value.ptr.as_ptr()),
            },
            _marker: PhantomData,
        }
    }

    /// Create a new argument from a file descriptor.
    #[must_use]
    pub fn from_fd(value: BorrowedFd<'a>) -> Self {
        Self {
            arg: InnerArgument {
                h: value.as_raw_fd(),
            },
            _marker: PhantomData,
        }
    }
}

impl<'a, 'dat> Argument<'a, 'dat> {
    /// Create a new argument from an array.
    #[must_use]
    pub fn from_array(value: &'a Array<'dat>) -> Self {
        Self {
            arg: InnerArgument {
                a: ptr::addr_of!(value.array).cast_mut(),
            },
            _marker: PhantomData,
        }
    }
}

/// Wayland array type.
#[repr(transparent)]
pub struct Array<'a> {
    array: InnerArray,
    _marker: PhantomData<&'a [u8]>,
}

impl fmt::Debug for Array<'_> {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        f.debug_tuple("Array").field(&self.as_slice()).finish()
    }
}

/// Inner representation of an array.
#[repr(C)]
struct InnerArray {
    size: usize,
    alloc: usize,
    data: *mut u8,
}

impl<'a> Array<'a> {
    /// Create a new array on the stack.
    #[must_use]
    pub fn new(data: &'a [u8]) -> Self {
        Self {
            array: InnerArray {
                size: data.len(),
                alloc: data.len(),
                data: data.as_ptr().cast_mut(),
            },
            _marker: PhantomData,
        }
    }

    /// Get the underlying data.
    #[must_use]
    pub fn as_slice(&self) -> &'a [u8] {
        unsafe { std::slice::from_raw_parts(self.array.data, self.array.size) }
    }
}

//
// Hardcoded Interfaces
//

static ANONYMOUS_INTERFACE: Interface = Interface::new(cstr!("<anonymous>"), 0, &[], &[]);

//
// Utilities
//

/// An error that occurs while opening a file.
#[derive(Debug)]
struct FileError {
    /// Path to the file.
    file: PathBuf,

    /// Underlying I/O error.
    error: io::Error,
}

impl FileError {
    #[inline]
    fn new(path: &Path, error: io::Error) -> Self {
        Self {
            file: path.to_path_buf(),
            error,
        }
    }
}

impl fmt::Display for FileError {
    #[inline]
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}: {}", self.file.display(), self.error)
    }
}

impl std::error::Error for FileError {
    #[inline]
    fn source(&self) -> Option<&(dyn std::error::Error + 'static)> {
        Some(&self.error)
    }
}
