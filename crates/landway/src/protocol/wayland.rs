// MIT/Apache2 License
// This file is automatically generated! Do not edit this file manually.
// Please see the landway-gen code for more info.

//! The `wayland` protocol.

#![allow(
    clippy::doc_lazy_continuation,
    clippy::doc_markdown,
    clippy::match_single_binding,
    clippy::missing_errors_doc,
    clippy::missing_panics_doc,
    clippy::unreadable_literal,
    clippy::redundant_closure_for_method_calls,
    clippy::too_many_lines,
    unused_imports,
    unused_mut
)]

#[allow(clippy::wildcard_imports)]
use super::__all::*;

// Original Wayland copyright information
// -------------------------------------------------
// 
// Copyright © 2008-2011 <PERSON><PERSON>
// Copyright © 2010-2011 Intel Corporation
// Copyright © 2012-2013 Collabora, Ltd.
// 
// Permission is hereby granted, free of charge, to any person
// obtaining a copy of this software and associated documentation files
// (the "Software"), to deal in the Software without restriction,
// including without limitation the rights to use, copy, modify, merge,
// publish, distribute, sublicense, and/or sell copies of the Software,
// and to permit persons to whom the Software is furnished to do so,
// subject to the following conditions:
// 
// The above copyright notice and this permission notice (including the
// next paragraph) shall be included in all copies or substantial
// portions of the Software.
// 
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
// EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
// NONINFRINGEMENT.  IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS
// BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN
// ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
// CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
// SOFTWARE.
// 
// -------------------------------------------------

pub mod wl_display {
    //!  wl_display: core global object
    //! 
    //!  
    //!  The core global object.  This is a special singleton object.  It
    //!  is used for internal Wayland protocol features.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_display` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlDisplay(Proxy);

    impl fmt::Debug for WlDisplay {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlDisplay").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlDisplay {
        fn from(proxy: Proxy) -> Self {
            WlDisplay(proxy)
        }
    }

    impl From<WlDisplay> for Proxy {
        fn from(proxy: WlDisplay) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlDisplay {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlDisplay {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlDisplay {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlDisplay {
        pub fn sync(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_callback::WlCallback> {
            //!  sync: asynchronous roundtrip
            //! 
            //!  
            //!  The sync request asks the server to emit the 'done' event
            //!  on the returned wl_callback object.  Since requests are
            //!  handled in-order and events are delivered in-order, this can
            //!  be used as a barrier to ensure all previous requests and the
            //!  resulting events have been handled.
            //!  
            //!  The object returned by this request will be destroyed by the
            //!  compositor after the callback is fired and as such the client must not
            //!  attempt to use it after that point.
            //!  
            //!  The callback_data passed in the callback is undefined and should be ignored.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_callback::WlCallback::INTERFACE,
                super::wl_callback::WlCallback::VERSION,
                event_queue
            )?;
            Ok(super::wl_callback::WlCallback::from(proxy))
        }

        pub fn get_registry(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_registry::WlRegistry> {
            //!  get_registry: get global registry object
            //! 
            //!  
            //!  This request creates a registry object that allows the client
            //!  to list and bind the global objects available from the
            //!  compositor.
            //!  
            //!  It should be noted that the server side resources consumed in
            //!  response to a get_registry request can only be released when the
            //!  client disconnects, not when the client side proxy is destroyed.
            //!  Therefore, clients should invoke get_registry as infrequently as
            //!  possible to avoid wasting memory.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_registry::WlRegistry::INTERFACE,
                super::wl_registry::WlRegistry::VERSION,
                event_queue
            )?;
            Ok(super::wl_registry::WlRegistry::from(proxy))
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let object_id = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let object_id = object_id.unwrap();

                        let code = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let message = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let message = message.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Error {
                            object_id: &object_id,
                            code,
                            message,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let id = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::DeleteId {
                            id,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  error: fatal error event
        /// 
        ///  
        ///  The error event is sent out when a fatal (non-recoverable)
        ///  error has occurred.  The object_id argument is the object
        ///  where the error occurred, most often in response to a request
        ///  to that object.  The code identifies the error and is defined
        ///  by the object interface.  As such, each interface defines its
        ///  own set of error codes.  The message is a brief description
        ///  of the error, for (debugging) convenience.
        ///  
        Error {
            /// object_id - object where the error occurred
            object_id: &'a Proxy,
            /// code - error code
            code: u32,
            /// message - error description
            message: &'a CStr,
        },
        ///  delete_id: acknowledge object ID deletion
        /// 
        ///  
        ///  This event is used internally by the object ID management
        ///  logic. When a client deletes an object that it had created,
        ///  the server will send this event to acknowledge that it has
        ///  seen the delete request. When the client receives this event,
        ///  it will know that it can safely reuse the object ID.
        ///  
        DeleteId {
            /// id - deleted object ID
            id: u32,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_display"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("sync"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_callback::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_registry"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_registry::INTERFACE), ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("error"),
            sig!("ous"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&crate::ANONYMOUS_INTERFACE), None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("delete_id"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    ///  error: global error values
    /// 
    ///  
    ///  These errors are global and can be emitted in response to any
    ///  server request.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// invalid_object - server couldn't find object
        pub const INVALID_OBJECT: Error = Error(0);

        /// invalid_method - method doesn't exist on the specified interface or malformed request
        pub const INVALID_METHOD: Error = Error(1);

        /// no_memory - server is out of memory
        pub const NO_MEMORY: Error = Error(2);

        /// implementation - implementation error in compositor
        pub const IMPLEMENTATION: Error = Error(3);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_display::WlDisplay;

pub mod wl_registry {
    //!  wl_registry: global registry object
    //! 
    //!  
    //!  The singleton global registry object.  The server has a number of
    //!  global objects that are available to all clients.  These objects
    //!  typically represent an actual object in the server (for example,
    //!  an input device) or they are singleton objects that provide
    //!  extension functionality.
    //!  
    //!  When a client creates a registry object, the registry object
    //!  will emit a global event for each global currently in the
    //!  registry.  Globals come and go as a result of device or
    //!  monitor hotplugs, reconfiguration or other events, and the
    //!  registry will send out global and global_remove events to
    //!  keep the client up to date with the changes.  To mark the end
    //!  of the initial burst of events, the client can use the
    //!  wl_display.sync request immediately after calling
    //!  wl_display.get_registry.
    //!  
    //!  A client can bind to a global object by using the bind
    //!  request.  This creates a client-side handle that lets the object
    //!  emit events to the client and lets the client invoke requests on
    //!  the object.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_registry` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlRegistry(Proxy);

    impl fmt::Debug for WlRegistry {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlRegistry").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlRegistry {
        fn from(proxy: Proxy) -> Self {
            WlRegistry(proxy)
        }
    }

    impl From<WlRegistry> for Proxy {
        fn from(proxy: WlRegistry) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlRegistry {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlRegistry {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlRegistry {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlRegistry {
        pub fn bind(
            &mut self,
            name: u32, 
            interface: &'static crate::Interface,
            version: u32,
            event_queue: &crate::EventQueue,
        ) -> io::Result<Proxy> {
            //!  bind: bind an object to the display
            //! 
            //!  
            //!  Binds a new, client-created object to the server using the
            //!  specified name as the identifier.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (UInt32, name),
                (String, Some(interface.name())),
                (UInt32, version),
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                interface,
                version,
                event_queue
            )?;
            Ok(proxy)
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let name = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let interface = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let interface = interface.unwrap();

                        let version = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Global {
                            name,
                            interface,
                            version,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let name = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::GlobalRemove {
                            name,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  global: announce global object
        /// 
        ///  
        ///  Notify the client of global objects.
        ///  
        ///  The event notifies the client that a global object with
        ///  the given name is now available, and it implements the
        ///  given version of the given interface.
        ///  
        Global {
            /// name - numeric name of the global object
            name: u32,
            /// interface - interface implemented by the object
            interface: &'a CStr,
            /// version - interface version
            version: u32,
        },
        ///  global_remove: announce removal of global object
        /// 
        ///  
        ///  Notify the client of removed global objects.
        ///  
        ///  This event notifies the client that the global identified
        ///  by name is no longer available.  If the client bound to
        ///  the global using the bind request, the client should now
        ///  destroy that object.
        ///  
        ///  The object remains valid and requests to the object will be
        ///  ignored until the client destroys it, to avoid races between
        ///  the global going away and a client sending a request to it.
        ///  
        GlobalRemove {
            /// name - numeric name of the global object
            name: u32,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_registry"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("bind"),
            sig!("usun"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, Some(&crate::ANONYMOUS_INTERFACE), ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("global"),
            sig!("usu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("global_remove"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

}

pub use self::wl_registry::WlRegistry;

pub mod wl_callback {
    //!  wl_callback: callback object
    //! 
    //!  
    //!  Clients can handle the 'done' event to get notified when
    //!  the related request is done.
    //!  
    //!  Note, because wl_callback objects are created from multiple independent
    //!  factory interfaces, the wl_callback interface is frozen at version 1.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_callback` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlCallback(Proxy);

    impl fmt::Debug for WlCallback {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlCallback").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlCallback {
        fn from(proxy: Proxy) -> Self {
            WlCallback(proxy)
        }
    }

    impl From<WlCallback> for Proxy {
        fn from(proxy: WlCallback) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlCallback {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlCallback {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlCallback {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlCallback {
        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let callback_data = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Done {
                            callback_data,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  done: done event
        /// 
        ///  
        ///  Notify the client when the related request is done.
        ///  
        Done {
            /// callback_data - request-specific data for the callback
            callback_data: u32,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_callback"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("done"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

}

pub use self::wl_callback::WlCallback;

pub mod wl_compositor {
    //!  wl_compositor: the compositor singleton
    //! 
    //!  
    //!  A compositor.  This object is a singleton global.  The
    //!  compositor is in charge of combining the contents of multiple
    //!  surfaces into one displayable output.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_compositor` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlCompositor(Proxy);

    impl fmt::Debug for WlCompositor {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlCompositor").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlCompositor {
        fn from(proxy: Proxy) -> Self {
            WlCompositor(proxy)
        }
    }

    impl From<WlCompositor> for Proxy {
        fn from(proxy: WlCompositor) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlCompositor {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlCompositor {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlCompositor {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlCompositor {
        pub fn create_surface(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_surface::WlSurface> {
            //!  create_surface: create new surface
            //! 
            //!  
            //!  Ask the compositor to create a new surface.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_surface::WlSurface::INTERFACE,
                super::wl_surface::WlSurface::VERSION,
                event_queue
            )?;
            Ok(super::wl_surface::WlSurface::from(proxy))
        }

        pub fn create_region(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_region::WlRegion> {
            //!  create_region: create new region
            //! 
            //!  
            //!  Ask the compositor to create a new region.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_region::WlRegion::INTERFACE,
                super::wl_region::WlRegion::VERSION,
                event_queue
            )?;
            Ok(super::wl_region::WlRegion::from(proxy))
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 6;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_compositor"),
        6,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("create_surface"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_surface::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("create_region"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_region::INTERFACE), ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

}

pub use self::wl_compositor::WlCompositor;

pub mod wl_shm_pool {
    //!  wl_shm_pool: a shared memory pool
    //! 
    //!  
    //!  The wl_shm_pool object encapsulates a piece of memory shared
    //!  between the compositor and client.  Through the wl_shm_pool
    //!  object, the client can allocate shared memory wl_buffer objects.
    //!  All objects created through the same pool share the same
    //!  underlying mapped memory. Reusing the mapped memory avoids the
    //!  setup/teardown overhead and is useful when interactively resizing
    //!  a surface or for many small buffers.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_shm_pool` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlShmPool(Proxy);

    impl fmt::Debug for WlShmPool {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlShmPool").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlShmPool {
        fn from(proxy: Proxy) -> Self {
            WlShmPool(proxy)
        }
    }

    impl From<WlShmPool> for Proxy {
        fn from(proxy: WlShmPool) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlShmPool {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlShmPool {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlShmPool {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlShmPool {
        pub fn create_buffer(
            &mut self,
            event_queue: &crate::EventQueue,
            offset: i32,
            width: i32,
            height: i32,
            stride: i32,
            format: super::wl_shm::Format,
        ) -> io::Result<super::wl_buffer::WlBuffer> {
            //!  create_buffer: create a buffer from the pool
            //! 
            //!  
            //!  Create a wl_buffer object from the pool.
            //!  
            //!  The buffer is created offset bytes into the pool and has
            //!  width and height as specified.  The stride argument specifies
            //!  the number of bytes from the beginning of one row to the beginning
            //!  of the next.  The format is the pixel format of the buffer and
            //!  must be one of those advertised through the wl_shm.format event.
            //!  
            //!  A buffer will keep a reference to the pool it was created from
            //!  so it is valid to destroy the pool immediately after creating
            //!  a buffer from it.
            //!  

            const OPCODE: u32 = 0;

            let format = format.into();
            crate::args!(args = 
                (NewId, 0),
                (Int32, offset),
                (Int32, width),
                (Int32, height),
                (Int32, stride),
                (UInt32, format),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_buffer::WlBuffer::INTERFACE,
                super::wl_buffer::WlBuffer::VERSION,
                event_queue
            )?;
            Ok(super::wl_buffer::WlBuffer::from(proxy))
        }

        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy the pool
            //! 
            //!  
            //!  Destroy the shared memory pool.
            //!  
            //!  The mmapped memory will be released when all
            //!  buffers that have been created from this pool
            //!  are gone.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn resize(
            &mut self,
            size: i32,
        ) -> io::Result<()> {
            //!  resize: change the size of the pool mapping
            //! 
            //!  
            //!  This request will cause the server to remap the backing memory
            //!  for the pool from the file descriptor passed when the pool was
            //!  created, but using the new size.  This request can only be
            //!  used to make the pool bigger.
            //!  
            //!  This request only changes the amount of bytes that are mmapped
            //!  by the server and does not touch the file corresponding to the
            //!  file descriptor passed at creation time. It is the client's
            //!  responsibility to ensure that the file is at least as big as
            //!  the new pool size.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (Int32, size),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 2;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_shm_pool"),
        2,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("create_buffer"),
            sig!("niiiiu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_buffer::INTERFACE), None, None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("resize"),
            sig!("i"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

}

pub use self::wl_shm_pool::WlShmPool;

pub mod wl_shm {
    //!  wl_shm: shared memory support
    //! 
    //!  
    //!  A singleton global object that provides support for shared
    //!  memory.
    //!  
    //!  Clients can create wl_shm_pool objects using the create_pool
    //!  request.
    //!  
    //!  On binding the wl_shm object one or more format events
    //!  are emitted to inform clients about the valid pixel formats
    //!  that can be used for buffers.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_shm` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlShm(Proxy);

    impl fmt::Debug for WlShm {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlShm").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlShm {
        fn from(proxy: Proxy) -> Self {
            WlShm(proxy)
        }
    }

    impl From<WlShm> for Proxy {
        fn from(proxy: WlShm) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlShm {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlShm {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlShm {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlShm {
        pub fn create_pool(
            &mut self,
            event_queue: &crate::EventQueue,
            fd: impl AsFd, 
            size: i32,
        ) -> io::Result<super::wl_shm_pool::WlShmPool> {
            //!  create_pool: create a shm pool
            //! 
            //!  
            //!  Create a new wl_shm_pool object.
            //!  
            //!  The pool can be used to create shared memory based buffer
            //!  objects.  The server will mmap size bytes of the passed file
            //!  descriptor, to use as backing memory for the pool.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (NewId, 0),
                (FileDescriptor, fd.as_fd()),
                (Int32, size),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_shm_pool::WlShmPool::INTERFACE,
                super::wl_shm_pool::WlShmPool::VERSION,
                event_queue
            )?;
            Ok(super::wl_shm_pool::WlShmPool::from(proxy))
        }

        pub fn release(
            &mut self,
        ) -> io::Result<()> {
            //!  release: release the shm object
            //! 
            //!  
            //!  Using this request a client can tell the server that it is not going to
            //!  use the shm object anymore.
            //!  
            //!  Objects created via this interface remain unaffected.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let format = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let format = Format::from(format);

                        assert!(iter.next().is_none());
                        let event = Event::Format {
                            format,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 2;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  format: pixel format description
        /// 
        ///  
        ///  Informs the client about a valid pixel format that
        ///  can be used for buffers. Known formats include
        ///  argb8888 and xrgb8888.
        ///  
        Format {
            /// format - buffer pixel format
            format: Format,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_shm"),
        2,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("create_pool"),
            sig!("nhi"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_shm_pool::INTERFACE), None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("release"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("format"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    ///  error: wl_shm error values
    /// 
    ///  
    ///  These errors can be emitted in response to wl_shm requests.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// invalid_format - buffer format is not known
        pub const INVALID_FORMAT: Error = Error(0);

        /// invalid_stride - invalid size or stride during pool or buffer creation
        pub const INVALID_STRIDE: Error = Error(1);

        /// invalid_fd - mmapping the file descriptor failed
        pub const INVALID_FD: Error = Error(2);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

    ///  format: pixel formats
    /// 
    ///  
    ///  This describes the memory layout of an individual pixel.
    ///  
    ///  All renderers should support argb8888 and xrgb8888 but any other
    ///  formats are optional and may not be supported by the particular
    ///  renderer in use.
    ///  
    ///  The drm format codes match the macros defined in drm_fourcc.h, except
    ///  argb8888 and xrgb8888. The formats actually supported by the compositor
    ///  will be reported by the format event.
    ///  
    ///  For all wl_shm formats and unless specified in another protocol
    ///  extension, pre-multiplied alpha is used for pixel values.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Format(u32);

    impl Format {
        /// argb8888 - 32-bit ARGB format, [31:0] A:R:G:B 8:8:8:8 little endian
        pub const ARGB8888: Format = Format(0);

        /// xrgb8888 - 32-bit RGB format, [31:0] x:R:G:B 8:8:8:8 little endian
        pub const XRGB8888: Format = Format(1);

        /// c8 - 8-bit color index format, [7:0] C
        pub const C8: Format = Format(0x20203843);

        /// rgb332 - 8-bit RGB format, [7:0] R:G:B 3:3:2
        pub const RGB332: Format = Format(0x38424752);

        /// bgr233 - 8-bit BGR format, [7:0] B:G:R 2:3:3
        pub const BGR233: Format = Format(0x38524742);

        /// xrgb4444 - 16-bit xRGB format, [15:0] x:R:G:B 4:4:4:4 little endian
        pub const XRGB4444: Format = Format(0x32315258);

        /// xbgr4444 - 16-bit xBGR format, [15:0] x:B:G:R 4:4:4:4 little endian
        pub const XBGR4444: Format = Format(0x32314258);

        /// rgbx4444 - 16-bit RGBx format, [15:0] R:G:B:x 4:4:4:4 little endian
        pub const RGBX4444: Format = Format(0x32315852);

        /// bgrx4444 - 16-bit BGRx format, [15:0] B:G:R:x 4:4:4:4 little endian
        pub const BGRX4444: Format = Format(0x32315842);

        /// argb4444 - 16-bit ARGB format, [15:0] A:R:G:B 4:4:4:4 little endian
        pub const ARGB4444: Format = Format(0x32315241);

        /// abgr4444 - 16-bit ABGR format, [15:0] A:B:G:R 4:4:4:4 little endian
        pub const ABGR4444: Format = Format(0x32314241);

        /// rgba4444 - 16-bit RBGA format, [15:0] R:G:B:A 4:4:4:4 little endian
        pub const RGBA4444: Format = Format(0x32314152);

        /// bgra4444 - 16-bit BGRA format, [15:0] B:G:R:A 4:4:4:4 little endian
        pub const BGRA4444: Format = Format(0x32314142);

        /// xrgb1555 - 16-bit xRGB format, [15:0] x:R:G:B 1:5:5:5 little endian
        pub const XRGB1555: Format = Format(0x35315258);

        /// xbgr1555 - 16-bit xBGR 1555 format, [15:0] x:B:G:R 1:5:5:5 little endian
        pub const XBGR1555: Format = Format(0x35314258);

        /// rgbx5551 - 16-bit RGBx 5551 format, [15:0] R:G:B:x 5:5:5:1 little endian
        pub const RGBX5551: Format = Format(0x35315852);

        /// bgrx5551 - 16-bit BGRx 5551 format, [15:0] B:G:R:x 5:5:5:1 little endian
        pub const BGRX5551: Format = Format(0x35315842);

        /// argb1555 - 16-bit ARGB 1555 format, [15:0] A:R:G:B 1:5:5:5 little endian
        pub const ARGB1555: Format = Format(0x35315241);

        /// abgr1555 - 16-bit ABGR 1555 format, [15:0] A:B:G:R 1:5:5:5 little endian
        pub const ABGR1555: Format = Format(0x35314241);

        /// rgba5551 - 16-bit RGBA 5551 format, [15:0] R:G:B:A 5:5:5:1 little endian
        pub const RGBA5551: Format = Format(0x35314152);

        /// bgra5551 - 16-bit BGRA 5551 format, [15:0] B:G:R:A 5:5:5:1 little endian
        pub const BGRA5551: Format = Format(0x35314142);

        /// rgb565 - 16-bit RGB 565 format, [15:0] R:G:B 5:6:5 little endian
        pub const RGB565: Format = Format(0x36314752);

        /// bgr565 - 16-bit BGR 565 format, [15:0] B:G:R 5:6:5 little endian
        pub const BGR565: Format = Format(0x36314742);

        /// rgb888 - 24-bit RGB format, [23:0] R:G:B little endian
        pub const RGB888: Format = Format(0x34324752);

        /// bgr888 - 24-bit BGR format, [23:0] B:G:R little endian
        pub const BGR888: Format = Format(0x34324742);

        /// xbgr8888 - 32-bit xBGR format, [31:0] x:B:G:R 8:8:8:8 little endian
        pub const XBGR8888: Format = Format(0x34324258);

        /// rgbx8888 - 32-bit RGBx format, [31:0] R:G:B:x 8:8:8:8 little endian
        pub const RGBX8888: Format = Format(0x34325852);

        /// bgrx8888 - 32-bit BGRx format, [31:0] B:G:R:x 8:8:8:8 little endian
        pub const BGRX8888: Format = Format(0x34325842);

        /// abgr8888 - 32-bit ABGR format, [31:0] A:B:G:R 8:8:8:8 little endian
        pub const ABGR8888: Format = Format(0x34324241);

        /// rgba8888 - 32-bit RGBA format, [31:0] R:G:B:A 8:8:8:8 little endian
        pub const RGBA8888: Format = Format(0x34324152);

        /// bgra8888 - 32-bit BGRA format, [31:0] B:G:R:A 8:8:8:8 little endian
        pub const BGRA8888: Format = Format(0x34324142);

        /// xrgb2101010 - 32-bit xRGB format, [31:0] x:R:G:B 2:10:10:10 little endian
        pub const XRGB2101010: Format = Format(0x30335258);

        /// xbgr2101010 - 32-bit xBGR format, [31:0] x:B:G:R 2:10:10:10 little endian
        pub const XBGR2101010: Format = Format(0x30334258);

        /// rgbx1010102 - 32-bit RGBx format, [31:0] R:G:B:x 10:10:10:2 little endian
        pub const RGBX1010102: Format = Format(0x30335852);

        /// bgrx1010102 - 32-bit BGRx format, [31:0] B:G:R:x 10:10:10:2 little endian
        pub const BGRX1010102: Format = Format(0x30335842);

        /// argb2101010 - 32-bit ARGB format, [31:0] A:R:G:B 2:10:10:10 little endian
        pub const ARGB2101010: Format = Format(0x30335241);

        /// abgr2101010 - 32-bit ABGR format, [31:0] A:B:G:R 2:10:10:10 little endian
        pub const ABGR2101010: Format = Format(0x30334241);

        /// rgba1010102 - 32-bit RGBA format, [31:0] R:G:B:A 10:10:10:2 little endian
        pub const RGBA1010102: Format = Format(0x30334152);

        /// bgra1010102 - 32-bit BGRA format, [31:0] B:G:R:A 10:10:10:2 little endian
        pub const BGRA1010102: Format = Format(0x30334142);

        /// yuyv - packed YCbCr format, [31:0] Cr0:Y1:Cb0:Y0 8:8:8:8 little endian
        pub const YUYV: Format = Format(0x56595559);

        /// yvyu - packed YCbCr format, [31:0] Cb0:Y1:Cr0:Y0 8:8:8:8 little endian
        pub const YVYU: Format = Format(0x55595659);

        /// uyvy - packed YCbCr format, [31:0] Y1:Cr0:Y0:Cb0 8:8:8:8 little endian
        pub const UYVY: Format = Format(0x59565955);

        /// vyuy - packed YCbCr format, [31:0] Y1:Cb0:Y0:Cr0 8:8:8:8 little endian
        pub const VYUY: Format = Format(0x59555956);

        /// ayuv - packed AYCbCr format, [31:0] A:Y:Cb:Cr 8:8:8:8 little endian
        pub const AYUV: Format = Format(0x56555941);

        /// nv12 - 2 plane YCbCr Cr:Cb format, 2x2 subsampled Cr:Cb plane
        pub const NV12: Format = Format(0x3231564e);

        /// nv21 - 2 plane YCbCr Cb:Cr format, 2x2 subsampled Cb:Cr plane
        pub const NV21: Format = Format(0x3132564e);

        /// nv16 - 2 plane YCbCr Cr:Cb format, 2x1 subsampled Cr:Cb plane
        pub const NV16: Format = Format(0x3631564e);

        /// nv61 - 2 plane YCbCr Cb:Cr format, 2x1 subsampled Cb:Cr plane
        pub const NV61: Format = Format(0x3136564e);

        /// yuv410 - 3 plane YCbCr format, 4x4 subsampled Cb (1) and Cr (2) planes
        pub const YUV410: Format = Format(0x39565559);

        /// yvu410 - 3 plane YCbCr format, 4x4 subsampled Cr (1) and Cb (2) planes
        pub const YVU410: Format = Format(0x39555659);

        /// yuv411 - 3 plane YCbCr format, 4x1 subsampled Cb (1) and Cr (2) planes
        pub const YUV411: Format = Format(0x31315559);

        /// yvu411 - 3 plane YCbCr format, 4x1 subsampled Cr (1) and Cb (2) planes
        pub const YVU411: Format = Format(0x31315659);

        /// yuv420 - 3 plane YCbCr format, 2x2 subsampled Cb (1) and Cr (2) planes
        pub const YUV420: Format = Format(0x32315559);

        /// yvu420 - 3 plane YCbCr format, 2x2 subsampled Cr (1) and Cb (2) planes
        pub const YVU420: Format = Format(0x32315659);

        /// yuv422 - 3 plane YCbCr format, 2x1 subsampled Cb (1) and Cr (2) planes
        pub const YUV422: Format = Format(0x36315559);

        /// yvu422 - 3 plane YCbCr format, 2x1 subsampled Cr (1) and Cb (2) planes
        pub const YVU422: Format = Format(0x36315659);

        /// yuv444 - 3 plane YCbCr format, non-subsampled Cb (1) and Cr (2) planes
        pub const YUV444: Format = Format(0x34325559);

        /// yvu444 - 3 plane YCbCr format, non-subsampled Cr (1) and Cb (2) planes
        pub const YVU444: Format = Format(0x34325659);

        /// r8 - [7:0] R
        pub const R8: Format = Format(0x20203852);

        /// r16 - [15:0] R little endian
        pub const R16: Format = Format(0x20363152);

        /// rg88 - [15:0] R:G 8:8 little endian
        pub const RG88: Format = Format(0x38384752);

        /// gr88 - [15:0] G:R 8:8 little endian
        pub const GR88: Format = Format(0x38385247);

        /// rg1616 - [31:0] R:G 16:16 little endian
        pub const RG1616: Format = Format(0x32334752);

        /// gr1616 - [31:0] G:R 16:16 little endian
        pub const GR1616: Format = Format(0x32335247);

        /// xrgb16161616f - [63:0] x:R:G:B 16:16:16:16 little endian
        pub const XRGB16161616F: Format = Format(0x48345258);

        /// xbgr16161616f - [63:0] x:B:G:R 16:16:16:16 little endian
        pub const XBGR16161616F: Format = Format(0x48344258);

        /// argb16161616f - [63:0] A:R:G:B 16:16:16:16 little endian
        pub const ARGB16161616F: Format = Format(0x48345241);

        /// abgr16161616f - [63:0] A:B:G:R 16:16:16:16 little endian
        pub const ABGR16161616F: Format = Format(0x48344241);

        /// xyuv8888 - [31:0] X:Y:Cb:Cr 8:8:8:8 little endian
        pub const XYUV8888: Format = Format(0x56555958);

        /// vuy888 - [23:0] Cr:Cb:Y 8:8:8 little endian
        pub const VUY888: Format = Format(0x34325556);

        /// vuy101010 - Y followed by U then V, 10:10:10. Non-linear modifier only
        pub const VUY101010: Format = Format(0x30335556);

        /// y210 - [63:0] Cr0:0:Y1:0:Cb0:0:Y0:0 10:6:10:6:10:6:10:6 little endian per 2 Y pixels
        pub const Y210: Format = Format(0x30313259);

        /// y212 - [63:0] Cr0:0:Y1:0:Cb0:0:Y0:0 12:4:12:4:12:4:12:4 little endian per 2 Y pixels
        pub const Y212: Format = Format(0x32313259);

        /// y216 - [63:0] Cr0:Y1:Cb0:Y0 16:16:16:16 little endian per 2 Y pixels
        pub const Y216: Format = Format(0x36313259);

        /// y410 - [31:0] A:Cr:Y:Cb 2:10:10:10 little endian
        pub const Y410: Format = Format(0x30313459);

        /// y412 - [63:0] A:0:Cr:0:Y:0:Cb:0 12:4:12:4:12:4:12:4 little endian
        pub const Y412: Format = Format(0x32313459);

        /// y416 - [63:0] A:Cr:Y:Cb 16:16:16:16 little endian
        pub const Y416: Format = Format(0x36313459);

        /// xvyu2101010 - [31:0] X:Cr:Y:Cb 2:10:10:10 little endian
        pub const XVYU2101010: Format = Format(0x30335658);

        /// xvyu12_16161616 - [63:0] X:0:Cr:0:Y:0:Cb:0 12:4:12:4:12:4:12:4 little endian
        pub const XVYU12_16161616: Format = Format(0x36335658);

        /// xvyu16161616 - [63:0] X:Cr:Y:Cb 16:16:16:16 little endian
        pub const XVYU16161616: Format = Format(0x38345658);

        /// y0l0 - [63:0]   A3:A2:Y3:0:Cr0:0:Y2:0:A1:A0:Y1:0:Cb0:0:Y0:0  1:1:8:2:8:2:8:2:1:1:8:2:8:2:8:2 little endian
        pub const Y0L0: Format = Format(0x304c3059);

        /// x0l0 - [63:0]   X3:X2:Y3:0:Cr0:0:Y2:0:X1:X0:Y1:0:Cb0:0:Y0:0  1:1:8:2:8:2:8:2:1:1:8:2:8:2:8:2 little endian
        pub const X0L0: Format = Format(0x304c3058);

        /// y0l2 - [63:0]   A3:A2:Y3:Cr0:Y2:A1:A0:Y1:Cb0:Y0  1:1:10:10:10:1:1:10:10:10 little endian
        pub const Y0L2: Format = Format(0x324c3059);

        /// x0l2 - [63:0]   X3:X2:Y3:Cr0:Y2:X1:X0:Y1:Cb0:Y0  1:1:10:10:10:1:1:10:10:10 little endian
        pub const X0L2: Format = Format(0x324c3058);

        /// yuv420_8bit - 
        pub const YUV420_8BIT: Format = Format(0x38305559);

        /// yuv420_10bit - 
        pub const YUV420_10BIT: Format = Format(0x30315559);

        /// xrgb8888_a8 - 
        pub const XRGB8888_A8: Format = Format(0x38415258);

        /// xbgr8888_a8 - 
        pub const XBGR8888_A8: Format = Format(0x38414258);

        /// rgbx8888_a8 - 
        pub const RGBX8888_A8: Format = Format(0x38415852);

        /// bgrx8888_a8 - 
        pub const BGRX8888_A8: Format = Format(0x38415842);

        /// rgb888_a8 - 
        pub const RGB888_A8: Format = Format(0x38413852);

        /// bgr888_a8 - 
        pub const BGR888_A8: Format = Format(0x38413842);

        /// rgb565_a8 - 
        pub const RGB565_A8: Format = Format(0x38413552);

        /// bgr565_a8 - 
        pub const BGR565_A8: Format = Format(0x38413542);

        /// nv24 - non-subsampled Cr:Cb plane
        pub const NV24: Format = Format(0x3432564e);

        /// nv42 - non-subsampled Cb:Cr plane
        pub const NV42: Format = Format(0x3234564e);

        /// p210 - 2x1 subsampled Cr:Cb plane, 10 bit per channel
        pub const P210: Format = Format(0x30313250);

        /// p010 - 2x2 subsampled Cr:Cb plane 10 bits per channel
        pub const P010: Format = Format(0x30313050);

        /// p012 - 2x2 subsampled Cr:Cb plane 12 bits per channel
        pub const P012: Format = Format(0x32313050);

        /// p016 - 2x2 subsampled Cr:Cb plane 16 bits per channel
        pub const P016: Format = Format(0x36313050);

        /// axbxgxrx106106106106 - [63:0] A:x:B:x:G:x:R:x 10:6:10:6:10:6:10:6 little endian
        pub const AXBXGXRX106106106106: Format = Format(0x30314241);

        /// nv15 - 2x2 subsampled Cr:Cb plane
        pub const NV15: Format = Format(0x3531564e);

        /// q410 - 
        pub const Q410: Format = Format(0x30313451);

        /// q401 - 
        pub const Q401: Format = Format(0x31303451);

        /// xrgb16161616 - [63:0] x:R:G:B 16:16:16:16 little endian
        pub const XRGB16161616: Format = Format(0x38345258);

        /// xbgr16161616 - [63:0] x:B:G:R 16:16:16:16 little endian
        pub const XBGR16161616: Format = Format(0x38344258);

        /// argb16161616 - [63:0] A:R:G:B 16:16:16:16 little endian
        pub const ARGB16161616: Format = Format(0x38345241);

        /// abgr16161616 - [63:0] A:B:G:R 16:16:16:16 little endian
        pub const ABGR16161616: Format = Format(0x38344241);

        /// c1 - [7:0] C0:C1:C2:C3:C4:C5:C6:C7 1:1:1:1:1:1:1:1 eight pixels/byte
        pub const C1: Format = Format(0x20203143);

        /// c2 - [7:0] C0:C1:C2:C3 2:2:2:2 four pixels/byte
        pub const C2: Format = Format(0x20203243);

        /// c4 - [7:0] C0:C1 4:4 two pixels/byte
        pub const C4: Format = Format(0x20203443);

        /// d1 - [7:0] D0:D1:D2:D3:D4:D5:D6:D7 1:1:1:1:1:1:1:1 eight pixels/byte
        pub const D1: Format = Format(0x20203144);

        /// d2 - [7:0] D0:D1:D2:D3 2:2:2:2 four pixels/byte
        pub const D2: Format = Format(0x20203244);

        /// d4 - [7:0] D0:D1 4:4 two pixels/byte
        pub const D4: Format = Format(0x20203444);

        /// d8 - [7:0] D
        pub const D8: Format = Format(0x20203844);

        /// r1 - [7:0] R0:R1:R2:R3:R4:R5:R6:R7 1:1:1:1:1:1:1:1 eight pixels/byte
        pub const R1: Format = Format(0x20203152);

        /// r2 - [7:0] R0:R1:R2:R3 2:2:2:2 four pixels/byte
        pub const R2: Format = Format(0x20203252);

        /// r4 - [7:0] R0:R1 4:4 two pixels/byte
        pub const R4: Format = Format(0x20203452);

        /// r10 - [15:0] x:R 6:10 little endian
        pub const R10: Format = Format(0x20303152);

        /// r12 - [15:0] x:R 4:12 little endian
        pub const R12: Format = Format(0x20323152);

        /// avuy8888 - [31:0] A:Cr:Cb:Y 8:8:8:8 little endian
        pub const AVUY8888: Format = Format(0x59555641);

        /// xvuy8888 - [31:0] X:Cr:Cb:Y 8:8:8:8 little endian
        pub const XVUY8888: Format = Format(0x59555658);

        /// p030 - 2x2 subsampled Cr:Cb plane 10 bits per channel packed
        pub const P030: Format = Format(0x30333050);

    }

    impl From<u32> for Format {
        fn from(value: u32) -> Self {
            Format(value)
        }
    }

    impl From<i32> for Format {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Format::from(value as u32)
        }
    }

    impl From<Format> for u32 {
        fn from(value: Format) -> Self {
            value.0
        }
    }

    impl From<Format> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Format) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_shm::WlShm;

pub mod wl_buffer {
    //!  wl_buffer: content for a wl_surface
    //! 
    //!  
    //!  A buffer provides the content for a wl_surface. Buffers are
    //!  created through factory interfaces such as wl_shm, wp_linux_buffer_params
    //!  (from the linux-dmabuf protocol extension) or similar. It has a width and
    //!  a height and can be attached to a wl_surface, but the mechanism by which a
    //!  client provides and updates the contents is defined by the buffer factory
    //!  interface.
    //!  
    //!  Color channels are assumed to be electrical rather than optical (in other
    //!  words, encoded with a transfer function) unless otherwise specified. If
    //!  the buffer uses a format that has an alpha channel, the alpha channel is
    //!  assumed to be premultiplied into the electrical color channel values
    //!  (after transfer function encoding) unless otherwise specified.
    //!  
    //!  Note, because wl_buffer objects are created from multiple independent
    //!  factory interfaces, the wl_buffer interface is frozen at version 1.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_buffer` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlBuffer(Proxy);

    impl fmt::Debug for WlBuffer {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlBuffer").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlBuffer {
        fn from(proxy: Proxy) -> Self {
            WlBuffer(proxy)
        }
    }

    impl From<WlBuffer> for Proxy {
        fn from(proxy: WlBuffer) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlBuffer {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlBuffer {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlBuffer {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlBuffer {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy a buffer
            //! 
            //!  
            //!  Destroy a buffer. If and how you need to release the backing
            //!  storage is defined by the buffer factory interface.
            //!  
            //!  For possible side-effects to a surface, see wl_surface.attach.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        assert!(iter.next().is_none());
                        let event = Event::Release {
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  release: compositor releases buffer
        /// 
        ///  
        ///  Sent when this wl_buffer is no longer used by the compositor.
        ///  
        ///  For more information on when release events may or may not be sent,
        ///  and what consequences it has, please see the description of
        ///  wl_surface.attach.
        ///  
        ///  If a client receives a release event before the frame callback
        ///  requested in the same wl_surface.commit that attaches this
        ///  wl_buffer to a surface, then the client is immediately free to
        ///  reuse the buffer and its backing storage, and does not need a
        ///  second buffer for the next surface content update. Typically
        ///  this is possible, when the compositor maintains a copy of the
        ///  wl_surface contents, e.g. as a GL texture. This is an important
        ///  optimization for GL(ES) compositors with wl_shm clients.
        ///  
        Release {
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_buffer"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("release"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

}

pub use self::wl_buffer::WlBuffer;

pub mod wl_data_offer {
    //!  wl_data_offer: offer to transfer data
    //! 
    //!  
    //!  A wl_data_offer represents a piece of data offered for transfer
    //!  by another client (the source client).  It is used by the
    //!  copy-and-paste and drag-and-drop mechanisms.  The offer
    //!  describes the different mime types that the data can be
    //!  converted to and provides the mechanism for transferring the
    //!  data directly from the source client.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_data_offer` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlDataOffer(Proxy);

    impl fmt::Debug for WlDataOffer {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlDataOffer").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlDataOffer {
        fn from(proxy: Proxy) -> Self {
            WlDataOffer(proxy)
        }
    }

    impl From<WlDataOffer> for Proxy {
        fn from(proxy: WlDataOffer) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlDataOffer {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlDataOffer {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlDataOffer {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlDataOffer {
        pub fn accept(
            &mut self,
            serial: u32, 
            mime_type: Option<&CStr>, 
        ) -> io::Result<()> {
            //!  accept: accept one of the offered mime types
            //! 
            //!  
            //!  Indicate that the client can accept the given mime type, or
            //!  NULL for not accepted.
            //!  
            //!  For objects of version 2 or older, this request is used by the
            //!  client to give feedback whether the client can receive the given
            //!  mime type, or NULL if none is accepted; the feedback does not
            //!  determine whether the drag-and-drop operation succeeds or not.
            //!  
            //!  For objects of version 3 or newer, this request determines the
            //!  final result of the drag-and-drop operation. If the end result
            //!  is that no mime types were accepted, the drag-and-drop operation
            //!  will be cancelled and the corresponding drag source will receive
            //!  wl_data_source.cancelled. Clients may still use this event in
            //!  conjunction with wl_data_source.action for feedback.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (UInt32, serial),
                (NString, mime_type),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn receive(
            &mut self,
            mime_type: &CStr, 
            fd: impl AsFd, 
        ) -> io::Result<()> {
            //!  receive: request that the data is transferred
            //! 
            //!  
            //!  To transfer the offered data, the client issues this request
            //!  and indicates the mime type it wants to receive.  The transfer
            //!  happens through the passed file descriptor (typically created
            //!  with the pipe system call).  The source client writes the data
            //!  in the mime type representation requested and then closes the
            //!  file descriptor.
            //!  
            //!  The receiving client reads from the read end of the pipe until
            //!  EOF and then closes its end, at which point the transfer is
            //!  complete.
            //!  
            //!  This request may happen multiple times for different mime types,
            //!  both before and after wl_data_device.drop. Drag-and-drop destination
            //!  clients may preemptively fetch data or examine it more closely to
            //!  determine acceptance.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (String, Some(mime_type)),
                (FileDescriptor, fd.as_fd()),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy data offer
            //! 
            //!  
            //!  Destroy the data offer.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn finish(
            &mut self,
        ) -> io::Result<()> {
            //!  finish: the offer will no longer be used
            //! 
            //!  
            //!  Notifies the compositor that the drag destination successfully
            //!  finished the drag-and-drop operation.
            //!  
            //!  Upon receiving this request, the compositor will emit
            //!  wl_data_source.dnd_finished on the drag source client.
            //!  
            //!  It is a client error to perform other requests than
            //!  wl_data_offer.destroy after this one. It is also an error to perform
            //!  this request after a NULL mime type has been set in
            //!  wl_data_offer.accept or no action was received through
            //!  wl_data_offer.action.
            //!  
            //!  If wl_data_offer.finish request is received for a non drag and drop
            //!  operation, the invalid_finish protocol error is raised.
            //!  

            const OPCODE: u32 = 3;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_actions(
            &mut self,
            dnd_actions: super::wl_data_device_manager::DndAction,
            preferred_action: super::wl_data_device_manager::DndAction,
        ) -> io::Result<()> {
            //!  set_actions: set the available/preferred drag-and-drop actions
            //! 
            //!  
            //!  Sets the actions that the destination side client supports for
            //!  this operation. This request may trigger the emission of
            //!  wl_data_source.action and wl_data_offer.action events if the compositor
            //!  needs to change the selected action.
            //!  
            //!  This request can be called multiple times throughout the
            //!  drag-and-drop operation, typically in response to wl_data_device.enter
            //!  or wl_data_device.motion events.
            //!  
            //!  This request determines the final result of the drag-and-drop
            //!  operation. If the end result is that no action is accepted,
            //!  the drag source will receive wl_data_source.cancelled.
            //!  
            //!  The dnd_actions argument must contain only values expressed in the
            //!  wl_data_device_manager.dnd_actions enum, and the preferred_action
            //!  argument must only contain one of those values set, otherwise it
            //!  will result in a protocol error.
            //!  
            //!  While managing an "ask" action, the destination drag-and-drop client
            //!  may perform further wl_data_offer.receive requests, and is expected
            //!  to perform one last wl_data_offer.set_actions request with a preferred
            //!  action other than "ask" (and optionally wl_data_offer.accept) before
            //!  requesting wl_data_offer.finish, in order to convey the action selected
            //!  by the user. If the preferred action is not in the
            //!  wl_data_offer.source_actions mask, an error will be raised.
            //!  
            //!  If the "ask" action is dismissed (e.g. user cancellation), the client
            //!  is expected to perform wl_data_offer.destroy right away.
            //!  
            //!  This request can only be made on drag-and-drop offers, a protocol error
            //!  will be raised otherwise.
            //!  

            const OPCODE: u32 = 4;

            let dnd_actions = dnd_actions.into();
            let preferred_action = preferred_action.into();
            crate::args!(args = 
                (UInt32, dnd_actions),
                (UInt32, preferred_action),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let mime_type = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let mime_type = mime_type.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Offer {
                            mime_type,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let source_actions = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let source_actions = super::wl_data_device_manager::DndAction::from(source_actions);

                        assert!(iter.next().is_none());
                        let event = Event::SourceActions {
                            source_actions,
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        let dnd_action = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let dnd_action = super::wl_data_device_manager::DndAction::from(dnd_action);

                        assert!(iter.next().is_none());
                        let event = Event::Action {
                            dnd_action,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 3;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  offer: advertise offered mime type
        /// 
        ///  
        ///  Sent immediately after creating the wl_data_offer object.  One
        ///  event per offered mime type.
        ///  
        Offer {
            /// mime_type - offered mime type
            mime_type: &'a CStr,
        },
        ///  source_actions: notify the source-side available actions
        /// 
        ///  
        ///  This event indicates the actions offered by the data source. It
        ///  will be sent immediately after creating the wl_data_offer object,
        ///  or anytime the source side changes its offered actions through
        ///  wl_data_source.set_actions.
        ///  
        SourceActions {
            /// source_actions - actions offered by the data source
            source_actions: super::wl_data_device_manager::DndAction,
        },
        ///  action: notify the selected action
        /// 
        ///  
        ///  This event indicates the action selected by the compositor after
        ///  matching the source/destination side actions. Only one action (or
        ///  none) will be offered here.
        ///  
        ///  This event can be emitted multiple times during the drag-and-drop
        ///  operation in response to destination side action changes through
        ///  wl_data_offer.set_actions.
        ///  
        ///  This event will no longer be emitted after wl_data_device.drop
        ///  happened on the drag-and-drop destination, the client must
        ///  honor the last action received, or the last preferred one set
        ///  through wl_data_offer.set_actions when handling an "ask" action.
        ///  
        ///  Compositors may also change the selected action on the fly, mainly
        ///  in response to keyboard modifier changes during the drag-and-drop
        ///  operation.
        ///  
        ///  The most recent action received is always the valid one. Prior to
        ///  receiving wl_data_device.drop, the chosen action may change (e.g.
        ///  due to keyboard modifiers being pressed). At the time of receiving
        ///  wl_data_device.drop the drag-and-drop destination must honor the
        ///  last action received.
        ///  
        ///  Action changes may still happen after wl_data_device.drop,
        ///  especially on "ask" actions, where the drag-and-drop destination
        ///  may choose another action afterwards. Action changes happening
        ///  at this stage are always the result of inter-client negotiation, the
        ///  compositor shall no longer be able to induce a different action.
        ///  
        ///  Upon "ask" actions, it is expected that the drag-and-drop destination
        ///  may potentially choose a different action and/or mime type,
        ///  based on wl_data_offer.source_actions and finally chosen by the
        ///  user (e.g. popping up a menu with the available options). The
        ///  final wl_data_offer.set_actions and wl_data_offer.accept requests
        ///  must happen before the call to wl_data_offer.finish.
        ///  
        Action {
            /// dnd_action - action selected by the compositor
            dnd_action: super::wl_data_device_manager::DndAction,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_data_offer"),
        3,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("accept"),
            sig!("u?s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("receive"),
            sig!("sh"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("finish"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_actions"),
            sig!("uu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("offer"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("source_actions"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("action"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// invalid_finish - finish request was called untimely
        pub const INVALID_FINISH: Error = Error(0);

        /// invalid_action_mask - action mask contains invalid values
        pub const INVALID_ACTION_MASK: Error = Error(1);

        /// invalid_action - action argument has an invalid value
        pub const INVALID_ACTION: Error = Error(2);

        /// invalid_offer - offer doesn't accept this request
        pub const INVALID_OFFER: Error = Error(3);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_data_offer::WlDataOffer;

pub mod wl_data_source {
    //!  wl_data_source: offer to transfer data
    //! 
    //!  
    //!  The wl_data_source object is the source side of a wl_data_offer.
    //!  It is created by the source client in a data transfer and
    //!  provides a way to describe the offered data and a way to respond
    //!  to requests to transfer the data.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_data_source` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlDataSource(Proxy);

    impl fmt::Debug for WlDataSource {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlDataSource").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlDataSource {
        fn from(proxy: Proxy) -> Self {
            WlDataSource(proxy)
        }
    }

    impl From<WlDataSource> for Proxy {
        fn from(proxy: WlDataSource) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlDataSource {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlDataSource {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlDataSource {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlDataSource {
        pub fn offer(
            &mut self,
            mime_type: &CStr, 
        ) -> io::Result<()> {
            //!  offer: add an offered mime type
            //! 
            //!  
            //!  This request adds a mime type to the set of mime types
            //!  advertised to targets.  Can be called several times to offer
            //!  multiple types.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (String, Some(mime_type)),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy the data source
            //! 
            //!  
            //!  Destroy the data source.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_actions(
            &mut self,
            dnd_actions: super::wl_data_device_manager::DndAction,
        ) -> io::Result<()> {
            //!  set_actions: set the available drag-and-drop actions
            //! 
            //!  
            //!  Sets the actions that the source side client supports for this
            //!  operation. This request may trigger wl_data_source.action and
            //!  wl_data_offer.action events if the compositor needs to change the
            //!  selected action.
            //!  
            //!  The dnd_actions argument must contain only values expressed in the
            //!  wl_data_device_manager.dnd_actions enum, otherwise it will result
            //!  in a protocol error.
            //!  
            //!  This request must be made once only, and can only be made on sources
            //!  used in drag-and-drop, so it must be performed before
            //!  wl_data_device.start_drag. Attempting to use the source other than
            //!  for drag-and-drop will raise a protocol error.
            //!  

            const OPCODE: u32 = 2;

            let dnd_actions = dnd_actions.into();
            crate::args!(args = 
                (UInt32, dnd_actions),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let mime_type = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Target {
                            mime_type,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let mime_type = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let mime_type = mime_type.unwrap();

                        let fd = match iter.next() {
                            Some(crate::TypedArgument::FileDescriptor(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Send {
                            mime_type,
                            fd,
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        assert!(iter.next().is_none());
                        let event = Event::Cancelled {
                        };
                        listener(&proxy, event);
                    },
                    3 => {
                        assert!(iter.next().is_none());
                        let event = Event::DndDropPerformed {
                        };
                        listener(&proxy, event);
                    },
                    4 => {
                        assert!(iter.next().is_none());
                        let event = Event::DndFinished {
                        };
                        listener(&proxy, event);
                    },
                    5 => {
                        let dnd_action = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let dnd_action = super::wl_data_device_manager::DndAction::from(dnd_action);

                        assert!(iter.next().is_none());
                        let event = Event::Action {
                            dnd_action,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 3;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  target: a target accepts an offered mime type
        /// 
        ///  
        ///  Sent when a target accepts pointer_focus or motion events.  If
        ///  a target does not accept any of the offered types, type is NULL.
        ///  
        ///  Used for feedback during drag-and-drop.
        ///  
        Target {
            /// mime_type - mime type accepted by the target
            mime_type: Option<&'a CStr>,
        },
        ///  send: send the data
        /// 
        ///  
        ///  Request for data from the client.  Send the data as the
        ///  specified mime type over the passed file descriptor, then
        ///  close it.
        ///  
        Send {
            /// mime_type - mime type for the data
            mime_type: &'a CStr,
            /// fd - file descriptor for the data
            fd: std::os::unix::io::BorrowedFd<'a>,
        },
        ///  cancelled: selection was cancelled
        /// 
        ///  
        ///  This data source is no longer valid. There are several reasons why
        ///  this could happen:
        ///  
        ///  - The data source has been replaced by another data source.
        ///  - The drag-and-drop operation was performed, but the drop destination
        ///  did not accept any of the mime types offered through
        ///  wl_data_source.target.
        ///  - The drag-and-drop operation was performed, but the drop destination
        ///  did not select any of the actions present in the mask offered through
        ///  wl_data_source.action.
        ///  - The drag-and-drop operation was performed but didn't happen over a
        ///  surface.
        ///  - The compositor cancelled the drag-and-drop operation (e.g. compositor
        ///  dependent timeouts to avoid stale drag-and-drop transfers).
        ///  
        ///  The client should clean up and destroy this data source.
        ///  
        ///  For objects of version 2 or older, wl_data_source.cancelled will
        ///  only be emitted if the data source was replaced by another data
        ///  source.
        ///  
        Cancelled {
        },
        ///  dnd_drop_performed: the drag-and-drop operation physically finished
        /// 
        ///  
        ///  The user performed the drop action. This event does not indicate
        ///  acceptance, wl_data_source.cancelled may still be emitted afterwards
        ///  if the drop destination does not accept any mime type.
        ///  
        ///  However, this event might however not be received if the compositor
        ///  cancelled the drag-and-drop operation before this event could happen.
        ///  
        ///  Note that the data_source may still be used in the future and should
        ///  not be destroyed here.
        ///  
        DndDropPerformed {
        },
        ///  dnd_finished: the drag-and-drop operation concluded
        /// 
        ///  
        ///  The drop destination finished interoperating with this data
        ///  source, so the client is now free to destroy this data source and
        ///  free all associated data.
        ///  
        ///  If the action used to perform the operation was "move", the
        ///  source can now delete the transferred data.
        ///  
        DndFinished {
        },
        ///  action: notify the selected action
        /// 
        ///  
        ///  This event indicates the action selected by the compositor after
        ///  matching the source/destination side actions. Only one action (or
        ///  none) will be offered here.
        ///  
        ///  This event can be emitted multiple times during the drag-and-drop
        ///  operation, mainly in response to destination side changes through
        ///  wl_data_offer.set_actions, and as the data device enters/leaves
        ///  surfaces.
        ///  
        ///  It is only possible to receive this event after
        ///  wl_data_source.dnd_drop_performed if the drag-and-drop operation
        ///  ended in an "ask" action, in which case the final wl_data_source.action
        ///  event will happen immediately before wl_data_source.dnd_finished.
        ///  
        ///  Compositors may also change the selected action on the fly, mainly
        ///  in response to keyboard modifier changes during the drag-and-drop
        ///  operation.
        ///  
        ///  The most recent action received is always the valid one. The chosen
        ///  action may change alongside negotiation (e.g. an "ask" action can turn
        ///  into a "move" operation), so the effects of the final action must
        ///  always be applied in wl_data_offer.dnd_finished.
        ///  
        ///  Clients can trigger cursor surface changes from this point, so
        ///  they reflect the current action.
        ///  
        Action {
            /// dnd_action - action selected by the compositor
            dnd_action: super::wl_data_device_manager::DndAction,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_data_source"),
        3,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("offer"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_actions"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("target"),
            sig!("?s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("send"),
            sig!("sh"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("cancelled"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("dnd_drop_performed"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("dnd_finished"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("action"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// invalid_action_mask - action mask contains invalid values
        pub const INVALID_ACTION_MASK: Error = Error(0);

        /// invalid_source - source doesn't accept this request
        pub const INVALID_SOURCE: Error = Error(1);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_data_source::WlDataSource;

pub mod wl_data_device {
    //!  wl_data_device: data transfer device
    //! 
    //!  
    //!  There is one wl_data_device per seat which can be obtained
    //!  from the global wl_data_device_manager singleton.
    //!  
    //!  A wl_data_device provides access to inter-client data transfer
    //!  mechanisms such as copy-and-paste and drag-and-drop.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_data_device` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlDataDevice(Proxy);

    impl fmt::Debug for WlDataDevice {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlDataDevice").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlDataDevice {
        fn from(proxy: Proxy) -> Self {
            WlDataDevice(proxy)
        }
    }

    impl From<WlDataDevice> for Proxy {
        fn from(proxy: WlDataDevice) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlDataDevice {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlDataDevice {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlDataDevice {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlDataDevice {
        pub fn start_drag(
            &mut self,
            source: Option<&super::wl_data_source::WlDataSource>, 
            origin: &super::wl_surface::WlSurface, 
            icon: Option<&super::wl_surface::WlSurface>, 
            serial: u32, 
        ) -> io::Result<()> {
            //!  start_drag: start drag-and-drop operation
            //! 
            //!  
            //!  This request asks the compositor to start a drag-and-drop
            //!  operation on behalf of the client.
            //!  
            //!  The source argument is the data source that provides the data
            //!  for the eventual data transfer. If source is NULL, enter, leave
            //!  and motion events are sent only to the client that initiated the
            //!  drag and the client is expected to handle the data passing
            //!  internally. If source is destroyed, the drag-and-drop session will be
            //!  cancelled.
            //!  
            //!  The origin surface is the surface where the drag originates and
            //!  the client must have an active implicit grab that matches the
            //!  serial.
            //!  
            //!  The icon surface is an optional (can be NULL) surface that
            //!  provides an icon to be moved around with the cursor.  Initially,
            //!  the top-left corner of the icon surface is placed at the cursor
            //!  hotspot, but subsequent wl_surface.offset requests can move the
            //!  relative position. Attach requests must be confirmed with
            //!  wl_surface.commit as usual. The icon surface is given the role of
            //!  a drag-and-drop icon. If the icon surface already has another role,
            //!  it raises a protocol error.
            //!  
            //!  The input region is ignored for wl_surfaces with the role of a
            //!  drag-and-drop icon.
            //!  
            //!  The given source may not be used in any further set_selection or
            //!  start_drag requests. Attempting to reuse a previously-used source
            //!  may send a used_source error.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (NObject, source.map(|x| x.as_proxy())),
                (Object, Some(origin.as_ref())),
                (NObject, icon.map(|x| x.as_proxy())),
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_selection(
            &mut self,
            source: Option<&super::wl_data_source::WlDataSource>, 
            serial: u32, 
        ) -> io::Result<()> {
            //!  set_selection: copy data to the selection
            //! 
            //!  
            //!  This request asks the compositor to set the selection
            //!  to the data from the source on behalf of the client.
            //!  
            //!  To unset the selection, set the source to NULL.
            //!  
            //!  The given source may not be used in any further set_selection or
            //!  start_drag requests. Attempting to reuse a previously-used source
            //!  may send a used_source error.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NObject, source.map(|x| x.as_proxy())),
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn release(
            &mut self,
        ) -> io::Result<()> {
            //!  release: destroy data device
            //! 
            //!  
            //!  This request destroys the data device.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let id = match iter.next() {
                            Some(crate::TypedArgument::NewId(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let id = id.map(super::wl_data_offer::WlDataOffer::from);
                        let id = id.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::DataOffer {
                            id,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let surface = surface.map(super::wl_surface::WlSurface::from);
                        let surface = surface.unwrap();

                        let x = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let y = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let id = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let id = id.map(super::wl_data_offer::WlDataOffer::from);

                        assert!(iter.next().is_none());
                        let event = Event::Enter {
                            serial,
                            surface: &surface,
                            x,
                            y,
                            id: id.as_ref(),
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        assert!(iter.next().is_none());
                        let event = Event::Leave {
                        };
                        listener(&proxy, event);
                    },
                    3 => {
                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let x = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let y = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Motion {
                            time,
                            x,
                            y,
                        };
                        listener(&proxy, event);
                    },
                    4 => {
                        assert!(iter.next().is_none());
                        let event = Event::Drop {
                        };
                        listener(&proxy, event);
                    },
                    5 => {
                        let id = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let id = id.map(super::wl_data_offer::WlDataOffer::from);

                        assert!(iter.next().is_none());
                        let event = Event::Selection {
                            id: id.as_ref(),
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 3;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  data_offer: introduce a new wl_data_offer
        /// 
        ///  
        ///  The data_offer event introduces a new wl_data_offer object,
        ///  which will subsequently be used in either the
        ///  data_device.enter event (for drag-and-drop) or the
        ///  data_device.selection event (for selections).  Immediately
        ///  following the data_device.data_offer event, the new data_offer
        ///  object will send out data_offer.offer events to describe the
        ///  mime types it offers.
        ///  
        DataOffer {
            /// id - the new data_offer object
            id: super::wl_data_offer::WlDataOffer,
        },
        ///  enter: initiate drag-and-drop session
        /// 
        ///  
        ///  This event is sent when an active drag-and-drop pointer enters
        ///  a surface owned by the client.  The position of the pointer at
        ///  enter time is provided by the x and y arguments, in surface-local
        ///  coordinates.
        ///  
        Enter {
            /// serial - serial number of the enter event
            serial: u32,
            /// surface - client surface entered
            surface: &'a super::wl_surface::WlSurface,
            /// x - surface-local x coordinate
            x: crate::Fixed,
            /// y - surface-local y coordinate
            y: crate::Fixed,
            /// id - source data_offer object
            id: Option<&'a super::wl_data_offer::WlDataOffer>,
        },
        ///  leave: end drag-and-drop session
        /// 
        ///  
        ///  This event is sent when the drag-and-drop pointer leaves the
        ///  surface and the session ends.  The client must destroy the
        ///  wl_data_offer introduced at enter time at this point.
        ///  
        Leave {
        },
        ///  motion: drag-and-drop session motion
        /// 
        ///  
        ///  This event is sent when the drag-and-drop pointer moves within
        ///  the currently focused surface. The new position of the pointer
        ///  is provided by the x and y arguments, in surface-local
        ///  coordinates.
        ///  
        Motion {
            /// time - timestamp with millisecond granularity
            time: u32,
            /// x - surface-local x coordinate
            x: crate::Fixed,
            /// y - surface-local y coordinate
            y: crate::Fixed,
        },
        ///  drop: end drag-and-drop session successfully
        /// 
        ///  
        ///  The event is sent when a drag-and-drop operation is ended
        ///  because the implicit grab is removed.
        ///  
        ///  The drag-and-drop destination is expected to honor the last action
        ///  received through wl_data_offer.action, if the resulting action is
        ///  "copy" or "move", the destination can still perform
        ///  wl_data_offer.receive requests, and is expected to end all
        ///  transfers with a wl_data_offer.finish request.
        ///  
        ///  If the resulting action is "ask", the action will not be considered
        ///  final. The drag-and-drop destination is expected to perform one last
        ///  wl_data_offer.set_actions request, or wl_data_offer.destroy in order
        ///  to cancel the operation.
        ///  
        Drop {
        },
        ///  selection: advertise new selection
        /// 
        ///  
        ///  The selection event is sent out to notify the client of a new
        ///  wl_data_offer for the selection for this device.  The
        ///  data_device.data_offer and the data_offer.offer events are
        ///  sent out immediately before this event to introduce the data
        ///  offer object.  The selection event is sent to a client
        ///  immediately before receiving keyboard focus and when a new
        ///  selection is set while the client has keyboard focus.  The
        ///  data_offer is valid until a new data_offer or NULL is received
        ///  or until the client loses keyboard focus.  Switching surface with
        ///  keyboard focus within the same client doesn't mean a new selection
        ///  will be sent.  The client must destroy the previous selection
        ///  data_offer, if any, upon receiving this event.
        ///  
        Selection {
            /// id - selection data_offer object
            id: Option<&'a super::wl_data_offer::WlDataOffer>,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_data_device"),
        3,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("start_drag"),
            sig!("?oo?ou"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_data_source::INTERFACE), Some(&super::wl_surface::INTERFACE), Some(&super::wl_surface::INTERFACE), None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_selection"),
            sig!("?ou"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_data_source::INTERFACE), None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("release"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("data_offer"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_data_offer::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("enter"),
            sig!("uoff?o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, Some(&super::wl_surface::INTERFACE), None, None, Some(&super::wl_data_offer::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("leave"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("motion"),
            sig!("uff"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("drop"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("selection"),
            sig!("?o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_data_offer::INTERFACE), ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// role - given wl_surface has another role
        pub const ROLE: Error = Error(0);

        /// used_source - source has already been used
        pub const USED_SOURCE: Error = Error(1);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_data_device::WlDataDevice;

pub mod wl_data_device_manager {
    //!  wl_data_device_manager: data transfer interface
    //! 
    //!  
    //!  The wl_data_device_manager is a singleton global object that
    //!  provides access to inter-client data transfer mechanisms such as
    //!  copy-and-paste and drag-and-drop.  These mechanisms are tied to
    //!  a wl_seat and this interface lets a client get a wl_data_device
    //!  corresponding to a wl_seat.
    //!  
    //!  Depending on the version bound, the objects created from the bound
    //!  wl_data_device_manager object will have different requirements for
    //!  functioning properly. See wl_data_source.set_actions,
    //!  wl_data_offer.accept and wl_data_offer.finish for details.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_data_device_manager` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlDataDeviceManager(Proxy);

    impl fmt::Debug for WlDataDeviceManager {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlDataDeviceManager").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlDataDeviceManager {
        fn from(proxy: Proxy) -> Self {
            WlDataDeviceManager(proxy)
        }
    }

    impl From<WlDataDeviceManager> for Proxy {
        fn from(proxy: WlDataDeviceManager) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlDataDeviceManager {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlDataDeviceManager {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlDataDeviceManager {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlDataDeviceManager {
        pub fn create_data_source(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_data_source::WlDataSource> {
            //!  create_data_source: create a new data source
            //! 
            //!  
            //!  Create a new data source.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_data_source::WlDataSource::INTERFACE,
                super::wl_data_source::WlDataSource::VERSION,
                event_queue
            )?;
            Ok(super::wl_data_source::WlDataSource::from(proxy))
        }

        pub fn get_data_device(
            &mut self,
            event_queue: &crate::EventQueue,
            seat: &super::wl_seat::WlSeat, 
        ) -> io::Result<super::wl_data_device::WlDataDevice> {
            //!  get_data_device: create a new data device
            //! 
            //!  
            //!  Create a new data device for a given seat.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NewId, 0),
                (Object, Some(seat.as_ref())),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_data_device::WlDataDevice::INTERFACE,
                super::wl_data_device::WlDataDevice::VERSION,
                event_queue
            )?;
            Ok(super::wl_data_device::WlDataDevice::from(proxy))
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 3;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_data_device_manager"),
        3,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("create_data_source"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_data_source::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_data_device"),
            sig!("no"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_data_device::INTERFACE), Some(&super::wl_seat::INTERFACE), ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

    ///  dnd_action: drag and drop actions
    /// 
    ///  
    ///  This is a bitmask of the available/preferred actions in a
    ///  drag-and-drop operation.
    ///  
    ///  In the compositor, the selected action is a result of matching the
    ///  actions offered by the source and destination sides.  "action" events
    ///  with a "none" action will be sent to both source and destination if
    ///  there is no match. All further checks will effectively happen on
    ///  (source actions ∩ destination actions).
    ///  
    ///  In addition, compositors may also pick different actions in
    ///  reaction to key modifiers being pressed. One common design that
    ///  is used in major toolkits (and the behavior recommended for
    ///  compositors) is:
    ///  
    ///  - If no modifiers are pressed, the first match (in bit order)
    ///  will be used.
    ///  - Pressing Shift selects "move", if enabled in the mask.
    ///  - Pressing Control selects "copy", if enabled in the mask.
    ///  
    ///  Behavior beyond that is considered implementation-dependent.
    ///  Compositors may for example bind other modifiers (like Alt/Meta)
    ///  or drags initiated with other buttons than BTN_LEFT to specific
    ///  actions (e.g. "ask").
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct DndAction(u32);

    impl DndAction {
        /// none - no action
        pub const NONE: DndAction = DndAction(0);

        /// copy - copy action
        pub const COPY: DndAction = DndAction(1);

        /// move - move action
        pub const MOVE: DndAction = DndAction(2);

        /// ask - ask action
        pub const ASK: DndAction = DndAction(4);

    }

    impl From<u32> for DndAction {
        fn from(value: u32) -> Self {
            DndAction(value)
        }
    }

    impl From<i32> for DndAction {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            DndAction::from(value as u32)
        }
    }

    impl From<DndAction> for u32 {
        fn from(value: DndAction) -> Self {
            value.0
        }
    }

    impl From<DndAction> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: DndAction) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_data_device_manager::WlDataDeviceManager;

pub mod wl_shell {
    //!  wl_shell: create desktop-style surfaces
    //! 
    //!  
    //!  This interface is implemented by servers that provide
    //!  desktop-style user interfaces.
    //!  
    //!  It allows clients to associate a wl_shell_surface with
    //!  a basic surface.
    //!  
    //!  Note! This protocol is deprecated and not intended for production use.
    //!  For desktop-style user interfaces, use xdg_shell. Compositors and clients
    //!  should not implement this interface.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_shell` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlShell(Proxy);

    impl fmt::Debug for WlShell {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlShell").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlShell {
        fn from(proxy: Proxy) -> Self {
            WlShell(proxy)
        }
    }

    impl From<WlShell> for Proxy {
        fn from(proxy: WlShell) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlShell {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlShell {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlShell {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlShell {
        pub fn get_shell_surface(
            &mut self,
            event_queue: &crate::EventQueue,
            surface: &super::wl_surface::WlSurface, 
        ) -> io::Result<super::wl_shell_surface::WlShellSurface> {
            //!  get_shell_surface: create a shell surface from a surface
            //! 
            //!  
            //!  Create a shell surface for an existing surface. This gives
            //!  the wl_surface the role of a shell surface. If the wl_surface
            //!  already has another role, it raises a protocol error.
            //!  
            //!  Only one shell surface can be associated with a given surface.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (NewId, 0),
                (Object, Some(surface.as_ref())),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_shell_surface::WlShellSurface::INTERFACE,
                super::wl_shell_surface::WlShellSurface::VERSION,
                event_queue
            )?;
            Ok(super::wl_shell_surface::WlShellSurface::from(proxy))
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_shell"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("get_shell_surface"),
            sig!("no"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_shell_surface::INTERFACE), Some(&super::wl_surface::INTERFACE), ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// role - given wl_surface has another role
        pub const ROLE: Error = Error(0);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_shell::WlShell;

pub mod wl_shell_surface {
    //!  wl_shell_surface: desktop-style metadata interface
    //! 
    //!  
    //!  An interface that may be implemented by a wl_surface, for
    //!  implementations that provide a desktop-style user interface.
    //!  
    //!  It provides requests to treat surfaces like toplevel, fullscreen
    //!  or popup windows, move, resize or maximize them, associate
    //!  metadata like title and class, etc.
    //!  
    //!  On the server side the object is automatically destroyed when
    //!  the related wl_surface is destroyed. On the client side,
    //!  wl_shell_surface_destroy() must be called before destroying
    //!  the wl_surface object.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_shell_surface` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlShellSurface(Proxy);

    impl fmt::Debug for WlShellSurface {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlShellSurface").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlShellSurface {
        fn from(proxy: Proxy) -> Self {
            WlShellSurface(proxy)
        }
    }

    impl From<WlShellSurface> for Proxy {
        fn from(proxy: WlShellSurface) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlShellSurface {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlShellSurface {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlShellSurface {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlShellSurface {
        pub fn pong(
            &mut self,
            serial: u32, 
        ) -> io::Result<()> {
            //!  pong: respond to a ping event
            //! 
            //!  
            //!  A client must respond to a ping event with a pong request or
            //!  the client may be deemed unresponsive.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn move_(
            &mut self,
            seat: &super::wl_seat::WlSeat, 
            serial: u32, 
        ) -> io::Result<()> {
            //!  move_: start an interactive move
            //! 
            //!  
            //!  Start a pointer-driven move of the surface.
            //!  
            //!  This request must be used in response to a button press event.
            //!  The server may ignore move requests depending on the state of
            //!  the surface (e.g. fullscreen or maximized).
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (Object, Some(seat.as_ref())),
                (UInt32, serial),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn resize(
            &mut self,
            seat: &super::wl_seat::WlSeat, 
            serial: u32, 
            edges: Resize,
        ) -> io::Result<()> {
            //!  resize: start an interactive resize
            //! 
            //!  
            //!  Start a pointer-driven resizing of the surface.
            //!  
            //!  This request must be used in response to a button press event.
            //!  The server may ignore resize requests depending on the state of
            //!  the surface (e.g. fullscreen or maximized).
            //!  

            const OPCODE: u32 = 2;

            let edges = edges.into();
            crate::args!(args = 
                (Object, Some(seat.as_ref())),
                (UInt32, serial),
                (UInt32, edges),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_toplevel(
            &mut self,
        ) -> io::Result<()> {
            //!  set_toplevel: make the surface a toplevel surface
            //! 
            //!  
            //!  Map the surface as a toplevel surface.
            //!  
            //!  A toplevel surface is not fullscreen, maximized or transient.
            //!  

            const OPCODE: u32 = 3;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_transient(
            &mut self,
            parent: &super::wl_surface::WlSurface, 
            x: i32,
            y: i32,
            flags: Transient,
        ) -> io::Result<()> {
            //!  set_transient: make the surface a transient surface
            //! 
            //!  
            //!  Map the surface relative to an existing surface.
            //!  
            //!  The x and y arguments specify the location of the upper left
            //!  corner of the surface relative to the upper left corner of the
            //!  parent surface, in surface-local coordinates.
            //!  
            //!  The flags argument controls details of the transient behaviour.
            //!  

            const OPCODE: u32 = 4;

            let flags = flags.into();
            crate::args!(args = 
                (Object, Some(parent.as_ref())),
                (Int32, x),
                (Int32, y),
                (UInt32, flags),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_fullscreen(
            &mut self,
            method: FullscreenMethod,
            framerate: u32, 
            output: Option<&super::wl_output::WlOutput>, 
        ) -> io::Result<()> {
            //!  set_fullscreen: make the surface a fullscreen surface
            //! 
            //!  
            //!  Map the surface as a fullscreen surface.
            //!  
            //!  If an output parameter is given then the surface will be made
            //!  fullscreen on that output. If the client does not specify the
            //!  output then the compositor will apply its policy - usually
            //!  choosing the output on which the surface has the biggest surface
            //!  area.
            //!  
            //!  The client may specify a method to resolve a size conflict
            //!  between the output size and the surface size - this is provided
            //!  through the method parameter.
            //!  
            //!  The framerate parameter is used only when the method is set
            //!  to "driver", to indicate the preferred framerate. A value of 0
            //!  indicates that the client does not care about framerate.  The
            //!  framerate is specified in mHz, that is framerate of 60000 is 60Hz.
            //!  
            //!  A method of "scale" or "driver" implies a scaling operation of
            //!  the surface, either via a direct scaling operation or a change of
            //!  the output mode. This will override any kind of output scaling, so
            //!  that mapping a surface with a buffer size equal to the mode can
            //!  fill the screen independent of buffer_scale.
            //!  
            //!  A method of "fill" means we don't scale up the buffer, however
            //!  any output scale is applied. This means that you may run into
            //!  an edge case where the application maps a buffer with the same
            //!  size of the output mode but buffer_scale 1 (thus making a
            //!  surface larger than the output). In this case it is allowed to
            //!  downscale the results to fit the screen.
            //!  
            //!  The compositor must reply to this request with a configure event
            //!  with the dimensions for the output on which the surface will
            //!  be made fullscreen.
            //!  

            const OPCODE: u32 = 5;

            let method = method.into();
            crate::args!(args = 
                (UInt32, method),
                (UInt32, framerate),
                (NObject, output.map(|x| x.as_proxy())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_popup(
            &mut self,
            seat: &super::wl_seat::WlSeat, 
            serial: u32, 
            parent: &super::wl_surface::WlSurface, 
            x: i32,
            y: i32,
            flags: Transient,
        ) -> io::Result<()> {
            //!  set_popup: make the surface a popup surface
            //! 
            //!  
            //!  Map the surface as a popup.
            //!  
            //!  A popup surface is a transient surface with an added pointer
            //!  grab.
            //!  
            //!  An existing implicit grab will be changed to owner-events mode,
            //!  and the popup grab will continue after the implicit grab ends
            //!  (i.e. releasing the mouse button does not cause the popup to
            //!  be unmapped).
            //!  
            //!  The popup grab continues until the window is destroyed or a
            //!  mouse button is pressed in any other client's window. A click
            //!  in any of the client's surfaces is reported as normal, however,
            //!  clicks in other clients' surfaces will be discarded and trigger
            //!  the callback.
            //!  
            //!  The x and y arguments specify the location of the upper left
            //!  corner of the surface relative to the upper left corner of the
            //!  parent surface, in surface-local coordinates.
            //!  

            const OPCODE: u32 = 6;

            let flags = flags.into();
            crate::args!(args = 
                (Object, Some(seat.as_ref())),
                (UInt32, serial),
                (Object, Some(parent.as_ref())),
                (Int32, x),
                (Int32, y),
                (UInt32, flags),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_maximized(
            &mut self,
            output: Option<&super::wl_output::WlOutput>, 
        ) -> io::Result<()> {
            //!  set_maximized: make the surface a maximized surface
            //! 
            //!  
            //!  Map the surface as a maximized surface.
            //!  
            //!  If an output parameter is given then the surface will be
            //!  maximized on that output. If the client does not specify the
            //!  output then the compositor will apply its policy - usually
            //!  choosing the output on which the surface has the biggest surface
            //!  area.
            //!  
            //!  The compositor will reply with a configure event telling
            //!  the expected new surface size. The operation is completed
            //!  on the next buffer attach to this surface.
            //!  
            //!  A maximized surface typically fills the entire output it is
            //!  bound to, except for desktop elements such as panels. This is
            //!  the main difference between a maximized shell surface and a
            //!  fullscreen shell surface.
            //!  
            //!  The details depend on the compositor implementation.
            //!  

            const OPCODE: u32 = 7;

            crate::args!(args = 
                (NObject, output.map(|x| x.as_proxy())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_title(
            &mut self,
            title: &CStr, 
        ) -> io::Result<()> {
            //!  set_title: set surface title
            //! 
            //!  
            //!  Set a short title for the surface.
            //!  
            //!  This string may be used to identify the surface in a task bar,
            //!  window list, or other user interface elements provided by the
            //!  compositor.
            //!  
            //!  The string must be encoded in UTF-8.
            //!  

            const OPCODE: u32 = 8;

            crate::args!(args = 
                (String, Some(title)),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_class(
            &mut self,
            class_: &CStr, 
        ) -> io::Result<()> {
            //!  set_class: set surface class
            //! 
            //!  
            //!  Set a class for the surface.
            //!  
            //!  The surface class identifies the general class of applications
            //!  to which the surface belongs. A common convention is to use the
            //!  file name (or the full path if it is a non-standard location) of
            //!  the application's .desktop file as the class.
            //!  

            const OPCODE: u32 = 9;

            crate::args!(args = 
                (String, Some(class_)),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Ping {
                            serial,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let edges = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let edges = Resize::from(edges);

                        let width = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let height = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Configure {
                            edges,
                            width,
                            height,
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        assert!(iter.next().is_none());
                        let event = Event::PopupDone {
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  ping: ping client
        /// 
        ///  
        ///  Ping a client to check if it is receiving events and sending
        ///  requests. A client is expected to reply with a pong request.
        ///  
        Ping {
            /// serial - serial number of the ping
            serial: u32,
        },
        ///  configure: suggest resize
        /// 
        ///  
        ///  The configure event asks the client to resize its surface.
        ///  
        ///  The size is a hint, in the sense that the client is free to
        ///  ignore it if it doesn't resize, pick a smaller size (to
        ///  satisfy aspect ratio or resize in steps of NxM pixels).
        ///  
        ///  The edges parameter provides a hint about how the surface
        ///  was resized. The client may use this information to decide
        ///  how to adjust its content to the new size (e.g. a scrolling
        ///  area might adjust its content position to leave the viewable
        ///  content unmoved).
        ///  
        ///  The client is free to dismiss all but the last configure
        ///  event it received.
        ///  
        ///  The width and height arguments specify the size of the window
        ///  in surface-local coordinates.
        ///  
        Configure {
            /// edges - how the surface was resized
            edges: Resize,
            /// width - new width of the surface
            width: i32,
            /// height - new height of the surface
            height: i32,
        },
        ///  popup_done: popup interaction is done
        /// 
        ///  
        ///  The popup_done event is sent out when a popup grab is broken,
        ///  that is, when the user clicks a surface that doesn't belong
        ///  to the client owning the popup surface.
        ///  
        PopupDone {
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_shell_surface"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("pong"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("move"),
            sig!("ou"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_seat::INTERFACE), None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("resize"),
            sig!("ouu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_seat::INTERFACE), None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_toplevel"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_transient"),
            sig!("oiiu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_surface::INTERFACE), None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_fullscreen"),
            sig!("uu?o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, Some(&super::wl_output::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_popup"),
            sig!("ouoiiu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_seat::INTERFACE), None, Some(&super::wl_surface::INTERFACE), None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_maximized"),
            sig!("?o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_output::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_title"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_class"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("ping"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("configure"),
            sig!("uii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("popup_done"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    ///  resize: edge values for resizing
    /// 
    ///  
    ///  These values are used to indicate which edge of a surface
    ///  is being dragged in a resize operation. The server may
    ///  use this information to adapt its behavior, e.g. choose
    ///  an appropriate cursor image.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Resize(u32);

    impl Resize {
        /// none - no edge
        pub const NONE: Resize = Resize(0);

        /// top - top edge
        pub const TOP: Resize = Resize(1);

        /// bottom - bottom edge
        pub const BOTTOM: Resize = Resize(2);

        /// left - left edge
        pub const LEFT: Resize = Resize(4);

        /// top_left - top and left edges
        pub const TOP_LEFT: Resize = Resize(5);

        /// bottom_left - bottom and left edges
        pub const BOTTOM_LEFT: Resize = Resize(6);

        /// right - right edge
        pub const RIGHT: Resize = Resize(8);

        /// top_right - top and right edges
        pub const TOP_RIGHT: Resize = Resize(9);

        /// bottom_right - bottom and right edges
        pub const BOTTOM_RIGHT: Resize = Resize(10);

    }

    impl From<u32> for Resize {
        fn from(value: u32) -> Self {
            Resize(value)
        }
    }

    impl From<i32> for Resize {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Resize::from(value as u32)
        }
    }

    impl From<Resize> for u32 {
        fn from(value: Resize) -> Self {
            value.0
        }
    }

    impl From<Resize> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Resize) -> Self {
            value.0 as i32
        }
    }

    ///  transient: details of transient behaviour
    /// 
    ///  
    ///  These flags specify details of the expected behaviour
    ///  of transient surfaces. Used in the set_transient request.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Transient(u32);

    impl Transient {
        /// inactive - do not set keyboard focus
        pub const INACTIVE: Transient = Transient(0x1);

    }

    impl From<u32> for Transient {
        fn from(value: u32) -> Self {
            Transient(value)
        }
    }

    impl From<i32> for Transient {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Transient::from(value as u32)
        }
    }

    impl From<Transient> for u32 {
        fn from(value: Transient) -> Self {
            value.0
        }
    }

    impl From<Transient> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Transient) -> Self {
            value.0 as i32
        }
    }

    ///  fullscreen_method: different method to set the surface fullscreen
    /// 
    ///  
    ///  Hints to indicate to the compositor how to deal with a conflict
    ///  between the dimensions of the surface and the dimensions of the
    ///  output. The compositor is free to ignore this parameter.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct FullscreenMethod(u32);

    impl FullscreenMethod {
        /// default - no preference, apply default policy
        pub const DEFAULT: FullscreenMethod = FullscreenMethod(0);

        /// scale - scale, preserve the surface's aspect ratio and center on output
        pub const SCALE: FullscreenMethod = FullscreenMethod(1);

        /// driver - switch output mode to the smallest mode that can fit the surface, add black borders to compensate size mismatch
        pub const DRIVER: FullscreenMethod = FullscreenMethod(2);

        /// fill - no upscaling, center on output and add black borders to compensate size mismatch
        pub const FILL: FullscreenMethod = FullscreenMethod(3);

    }

    impl From<u32> for FullscreenMethod {
        fn from(value: u32) -> Self {
            FullscreenMethod(value)
        }
    }

    impl From<i32> for FullscreenMethod {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            FullscreenMethod::from(value as u32)
        }
    }

    impl From<FullscreenMethod> for u32 {
        fn from(value: FullscreenMethod) -> Self {
            value.0
        }
    }

    impl From<FullscreenMethod> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: FullscreenMethod) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_shell_surface::WlShellSurface;

pub mod wl_surface {
    //!  wl_surface: an onscreen surface
    //! 
    //!  
    //!  A surface is a rectangular area that may be displayed on zero
    //!  or more outputs, and shown any number of times at the compositor's
    //!  discretion. They can present wl_buffers, receive user input, and
    //!  define a local coordinate system.
    //!  
    //!  The size of a surface (and relative positions on it) is described
    //!  in surface-local coordinates, which may differ from the buffer
    //!  coordinates of the pixel content, in case a buffer_transform
    //!  or a buffer_scale is used.
    //!  
    //!  A surface without a "role" is fairly useless: a compositor does
    //!  not know where, when or how to present it. The role is the
    //!  purpose of a wl_surface. Examples of roles are a cursor for a
    //!  pointer (as set by wl_pointer.set_cursor), a drag icon
    //!  (wl_data_device.start_drag), a sub-surface
    //!  (wl_subcompositor.get_subsurface), and a window as defined by a
    //!  shell protocol (e.g. wl_shell.get_shell_surface).
    //!  
    //!  A surface can have only one role at a time. Initially a
    //!  wl_surface does not have a role. Once a wl_surface is given a
    //!  role, it is set permanently for the whole lifetime of the
    //!  wl_surface object. Giving the current role again is allowed,
    //!  unless explicitly forbidden by the relevant interface
    //!  specification.
    //!  
    //!  Surface roles are given by requests in other interfaces such as
    //!  wl_pointer.set_cursor. The request should explicitly mention
    //!  that this request gives a role to a wl_surface. Often, this
    //!  request also creates a new protocol object that represents the
    //!  role and adds additional functionality to wl_surface. When a
    //!  client wants to destroy a wl_surface, they must destroy this role
    //!  object before the wl_surface, otherwise a defunct_role_object error is
    //!  sent.
    //!  
    //!  Destroying the role object does not remove the role from the
    //!  wl_surface, but it may stop the wl_surface from "playing the role".
    //!  For instance, if a wl_subsurface object is destroyed, the wl_surface
    //!  it was created for will be unmapped and forget its position and
    //!  z-order. It is allowed to create a wl_subsurface for the same
    //!  wl_surface again, but it is not allowed to use the wl_surface as
    //!  a cursor (cursor is a different role than sub-surface, and role
    //!  switching is not allowed).
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_surface` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlSurface(Proxy);

    impl fmt::Debug for WlSurface {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlSurface").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlSurface {
        fn from(proxy: Proxy) -> Self {
            WlSurface(proxy)
        }
    }

    impl From<WlSurface> for Proxy {
        fn from(proxy: WlSurface) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlSurface {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlSurface {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlSurface {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlSurface {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: delete surface
            //! 
            //!  
            //!  Deletes the surface and invalidates its object ID.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn attach(
            &mut self,
            buffer: Option<&super::wl_buffer::WlBuffer>, 
            x: i32,
            y: i32,
        ) -> io::Result<()> {
            //!  attach: set the surface contents
            //! 
            //!  
            //!  Set a buffer as the content of this surface.
            //!  
            //!  The new size of the surface is calculated based on the buffer
            //!  size transformed by the inverse buffer_transform and the
            //!  inverse buffer_scale. This means that at commit time the supplied
            //!  buffer size must be an integer multiple of the buffer_scale. If
            //!  that's not the case, an invalid_size error is sent.
            //!  
            //!  The x and y arguments specify the location of the new pending
            //!  buffer's upper left corner, relative to the current buffer's upper
            //!  left corner, in surface-local coordinates. In other words, the
            //!  x and y, combined with the new surface size define in which
            //!  directions the surface's size changes. Setting anything other than 0
            //!  as x and y arguments is discouraged, and should instead be replaced
            //!  with using the separate wl_surface.offset request.
            //!  
            //!  When the bound wl_surface version is 5 or higher, passing any
            //!  non-zero x or y is a protocol violation, and will result in an
            //!  'invalid_offset' error being raised. The x and y arguments are ignored
            //!  and do not change the pending state. To achieve equivalent semantics,
            //!  use wl_surface.offset.
            //!  
            //!  Surface contents are double-buffered state, see wl_surface.commit.
            //!  
            //!  The initial surface contents are void; there is no content.
            //!  wl_surface.attach assigns the given wl_buffer as the pending
            //!  wl_buffer. wl_surface.commit makes the pending wl_buffer the new
            //!  surface contents, and the size of the surface becomes the size
            //!  calculated from the wl_buffer, as described above. After commit,
            //!  there is no pending buffer until the next attach.
            //!  
            //!  Committing a pending wl_buffer allows the compositor to read the
            //!  pixels in the wl_buffer. The compositor may access the pixels at
            //!  any time after the wl_surface.commit request. When the compositor
            //!  will not access the pixels anymore, it will send the
            //!  wl_buffer.release event. Only after receiving wl_buffer.release,
            //!  the client may reuse the wl_buffer. A wl_buffer that has been
            //!  attached and then replaced by another attach instead of committed
            //!  will not receive a release event, and is not used by the
            //!  compositor.
            //!  
            //!  If a pending wl_buffer has been committed to more than one wl_surface,
            //!  the delivery of wl_buffer.release events becomes undefined. A well
            //!  behaved client should not rely on wl_buffer.release events in this
            //!  case. Alternatively, a client could create multiple wl_buffer objects
            //!  from the same backing storage or use a protocol extension providing
            //!  per-commit release notifications.
            //!  
            //!  Destroying the wl_buffer after wl_buffer.release does not change
            //!  the surface contents. Destroying the wl_buffer before wl_buffer.release
            //!  is allowed as long as the underlying buffer storage isn't re-used (this
            //!  can happen e.g. on client process termination). However, if the client
            //!  destroys the wl_buffer before receiving the wl_buffer.release event and
            //!  mutates the underlying buffer storage, the surface contents become
            //!  undefined immediately.
            //!  
            //!  If wl_surface.attach is sent with a NULL wl_buffer, the
            //!  following wl_surface.commit will remove the surface content.
            //!  
            //!  If a pending wl_buffer has been destroyed, the result is not specified.
            //!  Many compositors are known to remove the surface content on the following
            //!  wl_surface.commit, but this behaviour is not universal. Clients seeking to
            //!  maximise compatibility should not destroy pending buffers and should
            //!  ensure that they explicitly remove content from surfaces, even after
            //!  destroying buffers.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NObject, buffer.map(|x| x.as_proxy())),
                (Int32, x),
                (Int32, y),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn damage(
            &mut self,
            x: i32,
            y: i32,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  damage: mark part of the surface damaged
            //! 
            //!  
            //!  This request is used to describe the regions where the pending
            //!  buffer is different from the current surface contents, and where
            //!  the surface therefore needs to be repainted. The compositor
            //!  ignores the parts of the damage that fall outside of the surface.
            //!  
            //!  Damage is double-buffered state, see wl_surface.commit.
            //!  
            //!  The damage rectangle is specified in surface-local coordinates,
            //!  where x and y specify the upper left corner of the damage rectangle.
            //!  
            //!  The initial value for pending damage is empty: no damage.
            //!  wl_surface.damage adds pending damage: the new pending damage
            //!  is the union of old pending damage and the given rectangle.
            //!  
            //!  wl_surface.commit assigns pending damage as the current damage,
            //!  and clears pending damage. The server will clear the current
            //!  damage as it repaints the surface.
            //!  
            //!  Note! New clients should not use this request. Instead damage can be
            //!  posted with wl_surface.damage_buffer which uses buffer coordinates
            //!  instead of surface coordinates.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn frame(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_callback::WlCallback> {
            //!  frame: request a frame throttling hint
            //! 
            //!  
            //!  Request a notification when it is a good time to start drawing a new
            //!  frame, by creating a frame callback. This is useful for throttling
            //!  redrawing operations, and driving animations.
            //!  
            //!  When a client is animating on a wl_surface, it can use the 'frame'
            //!  request to get notified when it is a good time to draw and commit the
            //!  next frame of animation. If the client commits an update earlier than
            //!  that, it is likely that some updates will not make it to the display,
            //!  and the client is wasting resources by drawing too often.
            //!  
            //!  The frame request will take effect on the next wl_surface.commit.
            //!  The notification will only be posted for one frame unless
            //!  requested again. For a wl_surface, the notifications are posted in
            //!  the order the frame requests were committed.
            //!  
            //!  The server must send the notifications so that a client
            //!  will not send excessive updates, while still allowing
            //!  the highest possible update rate for clients that wait for the reply
            //!  before drawing again. The server should give some time for the client
            //!  to draw and commit after sending the frame callback events to let it
            //!  hit the next output refresh.
            //!  
            //!  A server should avoid signaling the frame callbacks if the
            //!  surface is not visible in any way, e.g. the surface is off-screen,
            //!  or completely obscured by other opaque surfaces.
            //!  
            //!  The object returned by this request will be destroyed by the
            //!  compositor after the callback is fired and as such the client must not
            //!  attempt to use it after that point.
            //!  
            //!  The callback_data passed in the callback is the current time, in
            //!  milliseconds, with an undefined base.
            //!  

            const OPCODE: u32 = 3;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_callback::WlCallback::INTERFACE,
                super::wl_callback::WlCallback::VERSION,
                event_queue
            )?;
            Ok(super::wl_callback::WlCallback::from(proxy))
        }

        pub fn set_opaque_region(
            &mut self,
            region: Option<&super::wl_region::WlRegion>, 
        ) -> io::Result<()> {
            //!  set_opaque_region: set opaque region
            //! 
            //!  
            //!  This request sets the region of the surface that contains
            //!  opaque content.
            //!  
            //!  The opaque region is an optimization hint for the compositor
            //!  that lets it optimize the redrawing of content behind opaque
            //!  regions.  Setting an opaque region is not required for correct
            //!  behaviour, but marking transparent content as opaque will result
            //!  in repaint artifacts.
            //!  
            //!  The opaque region is specified in surface-local coordinates.
            //!  
            //!  The compositor ignores the parts of the opaque region that fall
            //!  outside of the surface.
            //!  
            //!  Opaque region is double-buffered state, see wl_surface.commit.
            //!  
            //!  wl_surface.set_opaque_region changes the pending opaque region.
            //!  wl_surface.commit copies the pending region to the current region.
            //!  Otherwise, the pending and current regions are never changed.
            //!  
            //!  The initial value for an opaque region is empty. Setting the pending
            //!  opaque region has copy semantics, and the wl_region object can be
            //!  destroyed immediately. A NULL wl_region causes the pending opaque
            //!  region to be set to empty.
            //!  

            const OPCODE: u32 = 4;

            crate::args!(args = 
                (NObject, region.map(|x| x.as_proxy())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_input_region(
            &mut self,
            region: Option<&super::wl_region::WlRegion>, 
        ) -> io::Result<()> {
            //!  set_input_region: set input region
            //! 
            //!  
            //!  This request sets the region of the surface that can receive
            //!  pointer and touch events.
            //!  
            //!  Input events happening outside of this region will try the next
            //!  surface in the server surface stack. The compositor ignores the
            //!  parts of the input region that fall outside of the surface.
            //!  
            //!  The input region is specified in surface-local coordinates.
            //!  
            //!  Input region is double-buffered state, see wl_surface.commit.
            //!  
            //!  wl_surface.set_input_region changes the pending input region.
            //!  wl_surface.commit copies the pending region to the current region.
            //!  Otherwise the pending and current regions are never changed,
            //!  except cursor and icon surfaces are special cases, see
            //!  wl_pointer.set_cursor and wl_data_device.start_drag.
            //!  
            //!  The initial value for an input region is infinite. That means the
            //!  whole surface will accept input. Setting the pending input region
            //!  has copy semantics, and the wl_region object can be destroyed
            //!  immediately. A NULL wl_region causes the input region to be set
            //!  to infinite.
            //!  

            const OPCODE: u32 = 5;

            crate::args!(args = 
                (NObject, region.map(|x| x.as_proxy())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn commit(
            &mut self,
        ) -> io::Result<()> {
            //!  commit: commit pending surface state
            //! 
            //!  
            //!  Surface state (input, opaque, and damage regions, attached buffers,
            //!  etc.) is double-buffered. Protocol requests modify the pending state,
            //!  as opposed to the active state in use by the compositor.
            //!  
            //!  A commit request atomically creates a content update from the pending
            //!  state, even if the pending state has not been touched. The content
            //!  update is placed in a queue until it becomes active. After commit, the
            //!  new pending state is as documented for each related request.
            //!  
            //!  When the content update is applied, the wl_buffer is applied before all
            //!  other state. This means that all coordinates in double-buffered state
            //!  are relative to the newly attached wl_buffers, except for
            //!  wl_surface.attach itself. If there is no newly attached wl_buffer, the
            //!  coordinates are relative to the previous content update.
            //!  
            //!  All requests that need a commit to become effective are documented
            //!  to affect double-buffered state.
            //!  
            //!  Other interfaces may add further double-buffered surface state.
            //!  

            const OPCODE: u32 = 6;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_buffer_transform(
            &mut self,
            transform: super::wl_output::Transform,
        ) -> io::Result<()> {
            //!  set_buffer_transform: sets the buffer transformation
            //! 
            //!  
            //!  This request sets the transformation that the client has already applied
            //!  to the content of the buffer. The accepted values for the transform
            //!  parameter are the values for wl_output.transform.
            //!  
            //!  The compositor applies the inverse of this transformation whenever it
            //!  uses the buffer contents.
            //!  
            //!  Buffer transform is double-buffered state, see wl_surface.commit.
            //!  
            //!  A newly created surface has its buffer transformation set to normal.
            //!  
            //!  wl_surface.set_buffer_transform changes the pending buffer
            //!  transformation. wl_surface.commit copies the pending buffer
            //!  transformation to the current one. Otherwise, the pending and current
            //!  values are never changed.
            //!  
            //!  The purpose of this request is to allow clients to render content
            //!  according to the output transform, thus permitting the compositor to
            //!  use certain optimizations even if the display is rotated. Using
            //!  hardware overlays and scanning out a client buffer for fullscreen
            //!  surfaces are examples of such optimizations. Those optimizations are
            //!  highly dependent on the compositor implementation, so the use of this
            //!  request should be considered on a case-by-case basis.
            //!  
            //!  Note that if the transform value includes 90 or 270 degree rotation,
            //!  the width of the buffer will become the surface height and the height
            //!  of the buffer will become the surface width.
            //!  
            //!  If transform is not one of the values from the
            //!  wl_output.transform enum the invalid_transform protocol error
            //!  is raised.
            //!  

            const OPCODE: u32 = 7;

            let transform = transform.into();
            crate::args!(args = 
                (Int32, transform),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_buffer_scale(
            &mut self,
            scale: i32,
        ) -> io::Result<()> {
            //!  set_buffer_scale: sets the buffer scaling factor
            //! 
            //!  
            //!  This request sets an optional scaling factor on how the compositor
            //!  interprets the contents of the buffer attached to the window.
            //!  
            //!  Buffer scale is double-buffered state, see wl_surface.commit.
            //!  
            //!  A newly created surface has its buffer scale set to 1.
            //!  
            //!  wl_surface.set_buffer_scale changes the pending buffer scale.
            //!  wl_surface.commit copies the pending buffer scale to the current one.
            //!  Otherwise, the pending and current values are never changed.
            //!  
            //!  The purpose of this request is to allow clients to supply higher
            //!  resolution buffer data for use on high resolution outputs. It is
            //!  intended that you pick the same buffer scale as the scale of the
            //!  output that the surface is displayed on. This means the compositor
            //!  can avoid scaling when rendering the surface on that output.
            //!  
            //!  Note that if the scale is larger than 1, then you have to attach
            //!  a buffer that is larger (by a factor of scale in each dimension)
            //!  than the desired surface size.
            //!  
            //!  If scale is not greater than 0 the invalid_scale protocol error is
            //!  raised.
            //!  

            const OPCODE: u32 = 8;

            crate::args!(args = 
                (Int32, scale),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn damage_buffer(
            &mut self,
            x: i32,
            y: i32,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  damage_buffer: mark part of the surface damaged using buffer coordinates
            //! 
            //!  
            //!  This request is used to describe the regions where the pending
            //!  buffer is different from the current surface contents, and where
            //!  the surface therefore needs to be repainted. The compositor
            //!  ignores the parts of the damage that fall outside of the surface.
            //!  
            //!  Damage is double-buffered state, see wl_surface.commit.
            //!  
            //!  The damage rectangle is specified in buffer coordinates,
            //!  where x and y specify the upper left corner of the damage rectangle.
            //!  
            //!  The initial value for pending damage is empty: no damage.
            //!  wl_surface.damage_buffer adds pending damage: the new pending
            //!  damage is the union of old pending damage and the given rectangle.
            //!  
            //!  wl_surface.commit assigns pending damage as the current damage,
            //!  and clears pending damage. The server will clear the current
            //!  damage as it repaints the surface.
            //!  
            //!  This request differs from wl_surface.damage in only one way - it
            //!  takes damage in buffer coordinates instead of surface-local
            //!  coordinates. While this generally is more intuitive than surface
            //!  coordinates, it is especially desirable when using wp_viewport
            //!  or when a drawing library (like EGL) is unaware of buffer scale
            //!  and buffer transform.
            //!  
            //!  Note: Because buffer transformation changes and damage requests may
            //!  be interleaved in the protocol stream, it is impossible to determine
            //!  the actual mapping between surface and buffer damage until
            //!  wl_surface.commit time. Therefore, compositors wishing to take both
            //!  kinds of damage into account will have to accumulate damage from the
            //!  two requests separately and only transform from one to the other
            //!  after receiving the wl_surface.commit.
            //!  

            const OPCODE: u32 = 9;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn offset(
            &mut self,
            x: i32,
            y: i32,
        ) -> io::Result<()> {
            //!  offset: set the surface contents offset
            //! 
            //!  
            //!  The x and y arguments specify the location of the new pending
            //!  buffer's upper left corner, relative to the current buffer's upper
            //!  left corner, in surface-local coordinates. In other words, the
            //!  x and y, combined with the new surface size define in which
            //!  directions the surface's size changes.
            //!  
            //!  The exact semantics of wl_surface.offset are role-specific. Refer to
            //!  the documentation of specific roles for more information.
            //!  
            //!  Surface location offset is double-buffered state, see
            //!  wl_surface.commit.
            //!  
            //!  This request is semantically equivalent to and the replaces the x and y
            //!  arguments in the wl_surface.attach request in wl_surface versions prior
            //!  to 5. See wl_surface.attach for details.
            //!  

            const OPCODE: u32 = 10;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let output = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let output = output.map(super::wl_output::WlOutput::from);
                        let output = output.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Enter {
                            output: &output,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let output = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let output = output.map(super::wl_output::WlOutput::from);
                        let output = output.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Leave {
                            output: &output,
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        let factor = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::PreferredBufferScale {
                            factor,
                        };
                        listener(&proxy, event);
                    },
                    3 => {
                        let transform = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let transform = super::wl_output::Transform::from(transform);

                        assert!(iter.next().is_none());
                        let event = Event::PreferredBufferTransform {
                            transform,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 6;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  enter: surface enters an output
        /// 
        ///  
        ///  This is emitted whenever a surface's creation, movement, or resizing
        ///  results in some part of it being within the scanout region of an
        ///  output.
        ///  
        ///  Note that a surface may be overlapping with zero or more outputs.
        ///  
        Enter {
            /// output - output entered by the surface
            output: &'a super::wl_output::WlOutput,
        },
        ///  leave: surface leaves an output
        /// 
        ///  
        ///  This is emitted whenever a surface's creation, movement, or resizing
        ///  results in it no longer having any part of it within the scanout region
        ///  of an output.
        ///  
        ///  Clients should not use the number of outputs the surface is on for frame
        ///  throttling purposes. The surface might be hidden even if no leave event
        ///  has been sent, and the compositor might expect new surface content
        ///  updates even if no enter event has been sent. The frame event should be
        ///  used instead.
        ///  
        Leave {
            /// output - output left by the surface
            output: &'a super::wl_output::WlOutput,
        },
        ///  preferred_buffer_scale: preferred buffer scale for the surface
        /// 
        ///  
        ///  This event indicates the preferred buffer scale for this surface. It is
        ///  sent whenever the compositor's preference changes.
        ///  
        ///  Before receiving this event the preferred buffer scale for this surface
        ///  is 1.
        ///  
        ///  It is intended that scaling aware clients use this event to scale their
        ///  content and use wl_surface.set_buffer_scale to indicate the scale they
        ///  have rendered with. This allows clients to supply a higher detail
        ///  buffer.
        ///  
        ///  The compositor shall emit a scale value greater than 0.
        ///  
        PreferredBufferScale {
            /// factor - preferred scaling factor
            factor: i32,
        },
        ///  preferred_buffer_transform: preferred buffer transform for the surface
        /// 
        ///  
        ///  This event indicates the preferred buffer transform for this surface.
        ///  It is sent whenever the compositor's preference changes.
        ///  
        ///  Before receiving this event the preferred buffer transform for this
        ///  surface is normal.
        ///  
        ///  Applying this transformation to the surface buffer contents and using
        ///  wl_surface.set_buffer_transform might allow the compositor to use the
        ///  surface buffer more efficiently.
        ///  
        PreferredBufferTransform {
            /// transform - preferred transform
            transform: super::wl_output::Transform,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_surface"),
        6,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("attach"),
            sig!("?oii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_buffer::INTERFACE), None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("damage"),
            sig!("iiii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("frame"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_callback::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_opaque_region"),
            sig!("?o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_region::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_input_region"),
            sig!("?o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_region::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("commit"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_buffer_transform"),
            sig!("i"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_buffer_scale"),
            sig!("i"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("damage_buffer"),
            sig!("iiii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("offset"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("enter"),
            sig!("o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_output::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("leave"),
            sig!("o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_output::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("preferred_buffer_scale"),
            sig!("i"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("preferred_buffer_transform"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    ///  error: wl_surface error values
    /// 
    ///  
    ///  These errors can be emitted in response to wl_surface requests.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// invalid_scale - buffer scale value is invalid
        pub const INVALID_SCALE: Error = Error(0);

        /// invalid_transform - buffer transform value is invalid
        pub const INVALID_TRANSFORM: Error = Error(1);

        /// invalid_size - buffer size is invalid
        pub const INVALID_SIZE: Error = Error(2);

        /// invalid_offset - buffer offset is invalid
        pub const INVALID_OFFSET: Error = Error(3);

        /// defunct_role_object - surface was destroyed before its role object
        pub const DEFUNCT_ROLE_OBJECT: Error = Error(4);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_surface::WlSurface;

pub mod wl_seat {
    //!  wl_seat: group of input devices
    //! 
    //!  
    //!  A seat is a group of keyboards, pointer and touch devices. This
    //!  object is published as a global during start up, or when such a
    //!  device is hot plugged.  A seat typically has a pointer and
    //!  maintains a keyboard focus and a pointer focus.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_seat` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlSeat(Proxy);

    impl fmt::Debug for WlSeat {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlSeat").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlSeat {
        fn from(proxy: Proxy) -> Self {
            WlSeat(proxy)
        }
    }

    impl From<WlSeat> for Proxy {
        fn from(proxy: WlSeat) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlSeat {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlSeat {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlSeat {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlSeat {
        pub fn get_pointer(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_pointer::WlPointer> {
            //!  get_pointer: return pointer object
            //! 
            //!  
            //!  The ID provided will be initialized to the wl_pointer interface
            //!  for this seat.
            //!  
            //!  This request only takes effect if the seat has the pointer
            //!  capability, or has had the pointer capability in the past.
            //!  It is a protocol violation to issue this request on a seat that has
            //!  never had the pointer capability. The missing_capability error will
            //!  be sent in this case.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_pointer::WlPointer::INTERFACE,
                super::wl_pointer::WlPointer::VERSION,
                event_queue
            )?;
            Ok(super::wl_pointer::WlPointer::from(proxy))
        }

        pub fn get_keyboard(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_keyboard::WlKeyboard> {
            //!  get_keyboard: return keyboard object
            //! 
            //!  
            //!  The ID provided will be initialized to the wl_keyboard interface
            //!  for this seat.
            //!  
            //!  This request only takes effect if the seat has the keyboard
            //!  capability, or has had the keyboard capability in the past.
            //!  It is a protocol violation to issue this request on a seat that has
            //!  never had the keyboard capability. The missing_capability error will
            //!  be sent in this case.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_keyboard::WlKeyboard::INTERFACE,
                super::wl_keyboard::WlKeyboard::VERSION,
                event_queue
            )?;
            Ok(super::wl_keyboard::WlKeyboard::from(proxy))
        }

        pub fn get_touch(
            &mut self,
            event_queue: &crate::EventQueue,
        ) -> io::Result<super::wl_touch::WlTouch> {
            //!  get_touch: return touch object
            //! 
            //!  
            //!  The ID provided will be initialized to the wl_touch interface
            //!  for this seat.
            //!  
            //!  This request only takes effect if the seat has the touch
            //!  capability, or has had the touch capability in the past.
            //!  It is a protocol violation to issue this request on a seat that has
            //!  never had the touch capability. The missing_capability error will
            //!  be sent in this case.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (NewId, 0),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_touch::WlTouch::INTERFACE,
                super::wl_touch::WlTouch::VERSION,
                event_queue
            )?;
            Ok(super::wl_touch::WlTouch::from(proxy))
        }

        pub fn release(
            &mut self,
        ) -> io::Result<()> {
            //!  release: release the seat object
            //! 
            //!  
            //!  Using this request a client can tell the server that it is not going to
            //!  use the seat object anymore.
            //!  

            const OPCODE: u32 = 3;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let capabilities = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let capabilities = Capability::from(capabilities);

                        assert!(iter.next().is_none());
                        let event = Event::Capabilities {
                            capabilities,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let name = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let name = name.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Name {
                            name,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 10;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  capabilities: seat capabilities changed
        /// 
        ///  
        ///  This is sent on binding to the seat global or whenever a seat gains
        ///  or loses the pointer, keyboard or touch capabilities.
        ///  The argument is a capability enum containing the complete set of
        ///  capabilities this seat has.
        ///  
        ///  When the pointer capability is added, a client may create a
        ///  wl_pointer object using the wl_seat.get_pointer request. This object
        ///  will receive pointer events until the capability is removed in the
        ///  future.
        ///  
        ///  When the pointer capability is removed, a client should destroy the
        ///  wl_pointer objects associated with the seat where the capability was
        ///  removed, using the wl_pointer.release request. No further pointer
        ///  events will be received on these objects.
        ///  
        ///  In some compositors, if a seat regains the pointer capability and a
        ///  client has a previously obtained wl_pointer object of version 4 or
        ///  less, that object may start sending pointer events again. This
        ///  behavior is considered a misinterpretation of the intended behavior
        ///  and must not be relied upon by the client. wl_pointer objects of
        ///  version 5 or later must not send events if created before the most
        ///  recent event notifying the client of an added pointer capability.
        ///  
        ///  The above behavior also applies to wl_keyboard and wl_touch with the
        ///  keyboard and touch capabilities, respectively.
        ///  
        Capabilities {
            /// capabilities - capabilities of the seat
            capabilities: Capability,
        },
        ///  name: unique identifier for this seat
        /// 
        ///  
        ///  In a multi-seat configuration the seat name can be used by clients to
        ///  help identify which physical devices the seat represents.
        ///  
        ///  The seat name is a UTF-8 string with no convention defined for its
        ///  contents. Each name is unique among all wl_seat globals. The name is
        ///  only guaranteed to be unique for the current compositor instance.
        ///  
        ///  The same seat names are used for all clients. Thus, the name can be
        ///  shared across processes to refer to a specific wl_seat global.
        ///  
        ///  The name event is sent after binding to the seat global, and should be sent
        ///  before announcing capabilities. This event only sent once per seat object,
        ///  and the name does not change over the lifetime of the wl_seat global.
        ///  
        ///  Compositors may re-use the same seat name if the wl_seat global is
        ///  destroyed and re-created later.
        ///  
        Name {
            /// name - seat identifier
            name: &'a CStr,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_seat"),
        10,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("get_pointer"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_pointer::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_keyboard"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_keyboard::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_touch"),
            sig!("n"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_touch::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("release"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("capabilities"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("name"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    ///  capability: seat capability bitmask
    /// 
    ///  
    ///  This is a bitmask of capabilities this seat has; if a member is
    ///  set, then it is present on the seat.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Capability(u32);

    impl Capability {
        /// pointer - the seat has pointer devices
        pub const POINTER: Capability = Capability(1);

        /// keyboard - the seat has one or more keyboards
        pub const KEYBOARD: Capability = Capability(2);

        /// touch - the seat has touch devices
        pub const TOUCH: Capability = Capability(4);

    }

    impl From<u32> for Capability {
        fn from(value: u32) -> Self {
            Capability(value)
        }
    }

    impl From<i32> for Capability {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Capability::from(value as u32)
        }
    }

    impl From<Capability> for u32 {
        fn from(value: Capability) -> Self {
            value.0
        }
    }

    impl From<Capability> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Capability) -> Self {
            value.0 as i32
        }
    }

    ///  error: wl_seat error values
    /// 
    ///  
    ///  These errors can be emitted in response to wl_seat requests.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// missing_capability - get_pointer, get_keyboard or get_touch called on seat without the matching capability
        pub const MISSING_CAPABILITY: Error = Error(0);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_seat::WlSeat;

pub mod wl_pointer {
    //!  wl_pointer: pointer input device
    //! 
    //!  
    //!  The wl_pointer interface represents one or more input devices,
    //!  such as mice, which control the pointer location and pointer_focus
    //!  of a seat.
    //!  
    //!  The wl_pointer interface generates motion, enter and leave
    //!  events for the surfaces that the pointer is located over,
    //!  and button and axis events for button presses, button releases
    //!  and scrolling.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_pointer` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlPointer(Proxy);

    impl fmt::Debug for WlPointer {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlPointer").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlPointer {
        fn from(proxy: Proxy) -> Self {
            WlPointer(proxy)
        }
    }

    impl From<WlPointer> for Proxy {
        fn from(proxy: WlPointer) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlPointer {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlPointer {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlPointer {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlPointer {
        pub fn set_cursor(
            &mut self,
            serial: u32, 
            surface: Option<&super::wl_surface::WlSurface>, 
            hotspot_x: i32,
            hotspot_y: i32,
        ) -> io::Result<()> {
            //!  set_cursor: set the pointer surface
            //! 
            //!  
            //!  Set the pointer surface, i.e., the surface that contains the
            //!  pointer image (cursor). This request gives the surface the role
            //!  of a cursor. If the surface already has another role, it raises
            //!  a protocol error.
            //!  
            //!  The cursor actually changes only if the pointer
            //!  focus for this device is one of the requesting client's surfaces
            //!  or the surface parameter is the current pointer surface. If
            //!  there was a previous surface set with this request it is
            //!  replaced. If surface is NULL, the pointer image is hidden.
            //!  
            //!  The parameters hotspot_x and hotspot_y define the position of
            //!  the pointer surface relative to the pointer location. Its
            //!  top-left corner is always at (x, y) - (hotspot_x, hotspot_y),
            //!  where (x, y) are the coordinates of the pointer location, in
            //!  surface-local coordinates.
            //!  
            //!  On wl_surface.offset requests to the pointer surface, hotspot_x
            //!  and hotspot_y are decremented by the x and y parameters
            //!  passed to the request. The offset must be applied by
            //!  wl_surface.commit as usual.
            //!  
            //!  The hotspot can also be updated by passing the currently set
            //!  pointer surface to this request with new values for hotspot_x
            //!  and hotspot_y.
            //!  
            //!  The input region is ignored for wl_surfaces with the role of
            //!  a cursor. When the use as a cursor ends, the wl_surface is
            //!  unmapped.
            //!  
            //!  The serial parameter must match the latest wl_pointer.enter
            //!  serial number sent to the client. Otherwise the request will be
            //!  ignored.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
                (UInt32, serial),
                (NObject, surface.map(|x| x.as_proxy())),
                (Int32, hotspot_x),
                (Int32, hotspot_y),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn release(
            &mut self,
        ) -> io::Result<()> {
            //!  release: release the pointer object
            //! 
            //!  
            //!  Using this request a client can tell the server that it is not going to
            //!  use the pointer object anymore.
            //!  
            //!  This request destroys the pointer proxy object, so clients must not call
            //!  wl_pointer_destroy() after using this request.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let surface = surface.map(super::wl_surface::WlSurface::from);
                        let surface = surface.unwrap();

                        let surface_x = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface_y = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Enter {
                            serial,
                            surface: &surface,
                            surface_x,
                            surface_y,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let surface = surface.map(super::wl_surface::WlSurface::from);
                        let surface = surface.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Leave {
                            serial,
                            surface: &surface,
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface_x = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface_y = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Motion {
                            time,
                            surface_x,
                            surface_y,
                        };
                        listener(&proxy, event);
                    },
                    3 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let button = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let state = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let state = ButtonState::from(state);

                        assert!(iter.next().is_none());
                        let event = Event::Button {
                            serial,
                            time,
                            button,
                            state,
                        };
                        listener(&proxy, event);
                    },
                    4 => {
                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let axis = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let axis = Axis::from(axis);

                        let value = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Axis {
                            time,
                            axis,
                            value,
                        };
                        listener(&proxy, event);
                    },
                    5 => {
                        assert!(iter.next().is_none());
                        let event = Event::Frame {
                        };
                        listener(&proxy, event);
                    },
                    6 => {
                        let axis_source = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let axis_source = AxisSource::from(axis_source);

                        assert!(iter.next().is_none());
                        let event = Event::AxisSource {
                            axis_source,
                        };
                        listener(&proxy, event);
                    },
                    7 => {
                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let axis = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let axis = Axis::from(axis);

                        assert!(iter.next().is_none());
                        let event = Event::AxisStop {
                            time,
                            axis,
                        };
                        listener(&proxy, event);
                    },
                    8 => {
                        let axis = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let axis = Axis::from(axis);

                        let discrete = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::AxisDiscrete {
                            axis,
                            discrete,
                        };
                        listener(&proxy, event);
                    },
                    9 => {
                        let axis = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let axis = Axis::from(axis);

                        let value120 = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::AxisValue120 {
                            axis,
                            value120,
                        };
                        listener(&proxy, event);
                    },
                    10 => {
                        let axis = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let axis = Axis::from(axis);

                        let direction = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let direction = AxisRelativeDirection::from(direction);

                        assert!(iter.next().is_none());
                        let event = Event::AxisRelativeDirection {
                            axis,
                            direction,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 10;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  enter: enter event
        /// 
        ///  
        ///  Notification that this seat's pointer is focused on a certain
        ///  surface.
        ///  
        ///  When a seat's focus enters a surface, the pointer image
        ///  is undefined and a client should respond to this event by setting
        ///  an appropriate pointer image with the set_cursor request.
        ///  
        Enter {
            /// serial - serial number of the enter event
            serial: u32,
            /// surface - surface entered by the pointer
            surface: &'a super::wl_surface::WlSurface,
            /// surface_x - surface-local x coordinate
            surface_x: crate::Fixed,
            /// surface_y - surface-local y coordinate
            surface_y: crate::Fixed,
        },
        ///  leave: leave event
        /// 
        ///  
        ///  Notification that this seat's pointer is no longer focused on
        ///  a certain surface.
        ///  
        ///  The leave notification is sent before the enter notification
        ///  for the new focus.
        ///  
        Leave {
            /// serial - serial number of the leave event
            serial: u32,
            /// surface - surface left by the pointer
            surface: &'a super::wl_surface::WlSurface,
        },
        ///  motion: pointer motion event
        /// 
        ///  
        ///  Notification of pointer location change. The arguments
        ///  surface_x and surface_y are the location relative to the
        ///  focused surface.
        ///  
        Motion {
            /// time - timestamp with millisecond granularity
            time: u32,
            /// surface_x - surface-local x coordinate
            surface_x: crate::Fixed,
            /// surface_y - surface-local y coordinate
            surface_y: crate::Fixed,
        },
        ///  button: pointer button event
        /// 
        ///  
        ///  Mouse button click and release notifications.
        ///  
        ///  The location of the click is given by the last motion or
        ///  enter event.
        ///  The time argument is a timestamp with millisecond
        ///  granularity, with an undefined base.
        ///  
        ///  The button is a button code as defined in the Linux kernel's
        ///  linux/input-event-codes.h header file, e.g. BTN_LEFT.
        ///  
        ///  Any 16-bit button code value is reserved for future additions to the
        ///  kernel's event code list. All other button codes above 0xFFFF are
        ///  currently undefined but may be used in future versions of this
        ///  protocol.
        ///  
        Button {
            /// serial - serial number of the button event
            serial: u32,
            /// time - timestamp with millisecond granularity
            time: u32,
            /// button - button that produced the event
            button: u32,
            /// state - physical state of the button
            state: ButtonState,
        },
        ///  axis: axis event
        /// 
        ///  
        ///  Scroll and other axis notifications.
        ///  
        ///  For scroll events (vertical and horizontal scroll axes), the
        ///  value parameter is the length of a vector along the specified
        ///  axis in a coordinate space identical to those of motion events,
        ///  representing a relative movement along the specified axis.
        ///  
        ///  For devices that support movements non-parallel to axes multiple
        ///  axis events will be emitted.
        ///  
        ///  When applicable, for example for touch pads, the server can
        ///  choose to emit scroll events where the motion vector is
        ///  equivalent to a motion event vector.
        ///  
        ///  When applicable, a client can transform its content relative to the
        ///  scroll distance.
        ///  
        Axis {
            /// time - timestamp with millisecond granularity
            time: u32,
            /// axis - axis type
            axis: Axis,
            /// value - length of vector in surface-local coordinate space
            value: crate::Fixed,
        },
        ///  frame: end of a pointer event sequence
        /// 
        ///  
        ///  Indicates the end of a set of events that logically belong together.
        ///  A client is expected to accumulate the data in all events within the
        ///  frame before proceeding.
        ///  
        ///  All wl_pointer events before a wl_pointer.frame event belong
        ///  logically together. For example, in a diagonal scroll motion the
        ///  compositor will send an optional wl_pointer.axis_source event, two
        ///  wl_pointer.axis events (horizontal and vertical) and finally a
        ///  wl_pointer.frame event. The client may use this information to
        ///  calculate a diagonal vector for scrolling.
        ///  
        ///  When multiple wl_pointer.axis events occur within the same frame,
        ///  the motion vector is the combined motion of all events.
        ///  When a wl_pointer.axis and a wl_pointer.axis_stop event occur within
        ///  the same frame, this indicates that axis movement in one axis has
        ///  stopped but continues in the other axis.
        ///  When multiple wl_pointer.axis_stop events occur within the same
        ///  frame, this indicates that these axes stopped in the same instance.
        ///  
        ///  A wl_pointer.frame event is sent for every logical event group,
        ///  even if the group only contains a single wl_pointer event.
        ///  Specifically, a client may get a sequence: motion, frame, button,
        ///  frame, axis, frame, axis_stop, frame.
        ///  
        ///  The wl_pointer.enter and wl_pointer.leave events are logical events
        ///  generated by the compositor and not the hardware. These events are
        ///  also grouped by a wl_pointer.frame. When a pointer moves from one
        ///  surface to another, a compositor should group the
        ///  wl_pointer.leave event within the same wl_pointer.frame.
        ///  However, a client must not rely on wl_pointer.leave and
        ///  wl_pointer.enter being in the same wl_pointer.frame.
        ///  Compositor-specific policies may require the wl_pointer.leave and
        ///  wl_pointer.enter event being split across multiple wl_pointer.frame
        ///  groups.
        ///  
        Frame {
        },
        ///  axis_source: axis source event
        /// 
        ///  
        ///  Source information for scroll and other axes.
        ///  
        ///  This event does not occur on its own. It is sent before a
        ///  wl_pointer.frame event and carries the source information for
        ///  all events within that frame.
        ///  
        ///  The source specifies how this event was generated. If the source is
        ///  wl_pointer.axis_source.finger, a wl_pointer.axis_stop event will be
        ///  sent when the user lifts the finger off the device.
        ///  
        ///  If the source is wl_pointer.axis_source.wheel,
        ///  wl_pointer.axis_source.wheel_tilt or
        ///  wl_pointer.axis_source.continuous, a wl_pointer.axis_stop event may
        ///  or may not be sent. Whether a compositor sends an axis_stop event
        ///  for these sources is hardware-specific and implementation-dependent;
        ///  clients must not rely on receiving an axis_stop event for these
        ///  scroll sources and should treat scroll sequences from these scroll
        ///  sources as unterminated by default.
        ///  
        ///  This event is optional. If the source is unknown for a particular
        ///  axis event sequence, no event is sent.
        ///  Only one wl_pointer.axis_source event is permitted per frame.
        ///  
        ///  The order of wl_pointer.axis_discrete and wl_pointer.axis_source is
        ///  not guaranteed.
        ///  
        AxisSource {
            /// axis_source - source of the axis event
            axis_source: AxisSource,
        },
        ///  axis_stop: axis stop event
        /// 
        ///  
        ///  Stop notification for scroll and other axes.
        ///  
        ///  For some wl_pointer.axis_source types, a wl_pointer.axis_stop event
        ///  is sent to notify a client that the axis sequence has terminated.
        ///  This enables the client to implement kinetic scrolling.
        ///  See the wl_pointer.axis_source documentation for information on when
        ///  this event may be generated.
        ///  
        ///  Any wl_pointer.axis events with the same axis_source after this
        ///  event should be considered as the start of a new axis motion.
        ///  
        ///  The timestamp is to be interpreted identical to the timestamp in the
        ///  wl_pointer.axis event. The timestamp value may be the same as a
        ///  preceding wl_pointer.axis event.
        ///  
        AxisStop {
            /// time - timestamp with millisecond granularity
            time: u32,
            /// axis - the axis stopped with this event
            axis: Axis,
        },
        ///  axis_discrete: axis click event
        /// 
        ///  
        ///  Discrete step information for scroll and other axes.
        ///  
        ///  This event carries the axis value of the wl_pointer.axis event in
        ///  discrete steps (e.g. mouse wheel clicks).
        ///  
        ///  This event is deprecated with wl_pointer version 8 - this event is not
        ///  sent to clients supporting version 8 or later.
        ///  
        ///  This event does not occur on its own, it is coupled with a
        ///  wl_pointer.axis event that represents this axis value on a
        ///  continuous scale. The protocol guarantees that each axis_discrete
        ///  event is always followed by exactly one axis event with the same
        ///  axis number within the same wl_pointer.frame. Note that the protocol
        ///  allows for other events to occur between the axis_discrete and
        ///  its coupled axis event, including other axis_discrete or axis
        ///  events. A wl_pointer.frame must not contain more than one axis_discrete
        ///  event per axis type.
        ///  
        ///  This event is optional; continuous scrolling devices
        ///  like two-finger scrolling on touchpads do not have discrete
        ///  steps and do not generate this event.
        ///  
        ///  The discrete value carries the directional information. e.g. a value
        ///  of -2 is two steps towards the negative direction of this axis.
        ///  
        ///  The axis number is identical to the axis number in the associated
        ///  axis event.
        ///  
        ///  The order of wl_pointer.axis_discrete and wl_pointer.axis_source is
        ///  not guaranteed.
        ///  
        AxisDiscrete {
            /// axis - axis type
            axis: Axis,
            /// discrete - number of steps
            discrete: i32,
        },
        ///  axis_value120: axis high-resolution scroll event
        /// 
        ///  
        ///  Discrete high-resolution scroll information.
        ///  
        ///  This event carries high-resolution wheel scroll information,
        ///  with each multiple of 120 representing one logical scroll step
        ///  (a wheel detent). For example, an axis_value120 of 30 is one quarter of
        ///  a logical scroll step in the positive direction, a value120 of
        ///  -240 are two logical scroll steps in the negative direction within the
        ///  same hardware event.
        ///  Clients that rely on discrete scrolling should accumulate the
        ///  value120 to multiples of 120 before processing the event.
        ///  
        ///  The value120 must not be zero.
        ///  
        ///  This event replaces the wl_pointer.axis_discrete event in clients
        ///  supporting wl_pointer version 8 or later.
        ///  
        ///  Where a wl_pointer.axis_source event occurs in the same
        ///  wl_pointer.frame, the axis source applies to this event.
        ///  
        ///  The order of wl_pointer.axis_value120 and wl_pointer.axis_source is
        ///  not guaranteed.
        ///  
        AxisValue120 {
            /// axis - axis type
            axis: Axis,
            /// value120 - scroll distance as fraction of 120
            value120: i32,
        },
        ///  axis_relative_direction: axis relative physical direction event
        /// 
        ///  
        ///  Relative directional information of the entity causing the axis
        ///  motion.
        ///  
        ///  For a wl_pointer.axis event, the wl_pointer.axis_relative_direction
        ///  event specifies the movement direction of the entity causing the
        ///  wl_pointer.axis event. For example:
        ///  - if a user's fingers on a touchpad move down and this
        ///  causes a wl_pointer.axis vertical_scroll down event, the physical
        ///  direction is 'identical'
        ///  - if a user's fingers on a touchpad move down and this causes a
        ///  wl_pointer.axis vertical_scroll up scroll up event ('natural
        ///  scrolling'), the physical direction is 'inverted'.
        ///  
        ///  A client may use this information to adjust scroll motion of
        ///  components. Specifically, enabling natural scrolling causes the
        ///  content to change direction compared to traditional scrolling.
        ///  Some widgets like volume control sliders should usually match the
        ///  physical direction regardless of whether natural scrolling is
        ///  active. This event enables clients to match the scroll direction of
        ///  a widget to the physical direction.
        ///  
        ///  This event does not occur on its own, it is coupled with a
        ///  wl_pointer.axis event that represents this axis value.
        ///  The protocol guarantees that each axis_relative_direction event is
        ///  always followed by exactly one axis event with the same
        ///  axis number within the same wl_pointer.frame. Note that the protocol
        ///  allows for other events to occur between the axis_relative_direction
        ///  and its coupled axis event.
        ///  
        ///  The axis number is identical to the axis number in the associated
        ///  axis event.
        ///  
        ///  The order of wl_pointer.axis_relative_direction,
        ///  wl_pointer.axis_discrete and wl_pointer.axis_source is not
        ///  guaranteed.
        ///  
        AxisRelativeDirection {
            /// axis - axis type
            axis: Axis,
            /// direction - physical direction relative to axis motion
            direction: AxisRelativeDirection,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_pointer"),
        10,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("set_cursor"),
            sig!("u?oii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, Some(&super::wl_surface::INTERFACE), None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("release"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("enter"),
            sig!("uoff"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, Some(&super::wl_surface::INTERFACE), None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("leave"),
            sig!("uo"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, Some(&super::wl_surface::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("motion"),
            sig!("uff"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("button"),
            sig!("uuuu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("axis"),
            sig!("uuf"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("frame"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("axis_source"),
            sig!("u"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("axis_stop"),
            sig!("uu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("axis_discrete"),
            sig!("ui"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("axis_value120"),
            sig!("ui"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("axis_relative_direction"),
            sig!("uu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// role - given wl_surface has another role
        pub const ROLE: Error = Error(0);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

    ///  button_state: physical button state
    /// 
    ///  
    ///  Describes the physical state of a button that produced the button
    ///  event.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct ButtonState(u32);

    impl ButtonState {
        /// released - the button is not pressed
        pub const RELEASED: ButtonState = ButtonState(0);

        /// pressed - the button is pressed
        pub const PRESSED: ButtonState = ButtonState(1);

    }

    impl From<u32> for ButtonState {
        fn from(value: u32) -> Self {
            ButtonState(value)
        }
    }

    impl From<i32> for ButtonState {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            ButtonState::from(value as u32)
        }
    }

    impl From<ButtonState> for u32 {
        fn from(value: ButtonState) -> Self {
            value.0
        }
    }

    impl From<ButtonState> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: ButtonState) -> Self {
            value.0 as i32
        }
    }

    ///  axis: axis types
    /// 
    ///  
    ///  Describes the axis types of scroll events.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Axis(u32);

    impl Axis {
        /// vertical_scroll - vertical axis
        pub const VERTICAL_SCROLL: Axis = Axis(0);

        /// horizontal_scroll - horizontal axis
        pub const HORIZONTAL_SCROLL: Axis = Axis(1);

    }

    impl From<u32> for Axis {
        fn from(value: u32) -> Self {
            Axis(value)
        }
    }

    impl From<i32> for Axis {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Axis::from(value as u32)
        }
    }

    impl From<Axis> for u32 {
        fn from(value: Axis) -> Self {
            value.0
        }
    }

    impl From<Axis> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Axis) -> Self {
            value.0 as i32
        }
    }

    ///  axis_source: axis source types
    /// 
    ///  
    ///  Describes the source types for axis events. This indicates to the
    ///  client how an axis event was physically generated; a client may
    ///  adjust the user interface accordingly. For example, scroll events
    ///  from a "finger" source may be in a smooth coordinate space with
    ///  kinetic scrolling whereas a "wheel" source may be in discrete steps
    ///  of a number of lines.
    ///  
    ///  The "continuous" axis source is a device generating events in a
    ///  continuous coordinate space, but using something other than a
    ///  finger. One example for this source is button-based scrolling where
    ///  the vertical motion of a device is converted to scroll events while
    ///  a button is held down.
    ///  
    ///  The "wheel tilt" axis source indicates that the actual device is a
    ///  wheel but the scroll event is not caused by a rotation but a
    ///  (usually sideways) tilt of the wheel.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct AxisSource(u32);

    impl AxisSource {
        /// wheel - a physical wheel rotation
        pub const WHEEL: AxisSource = AxisSource(0);

        /// finger - finger on a touch surface
        pub const FINGER: AxisSource = AxisSource(1);

        /// continuous - continuous coordinate space
        pub const CONTINUOUS: AxisSource = AxisSource(2);

        /// wheel_tilt - a physical wheel tilt
        pub const WHEEL_TILT: AxisSource = AxisSource(3);

    }

    impl From<u32> for AxisSource {
        fn from(value: u32) -> Self {
            AxisSource(value)
        }
    }

    impl From<i32> for AxisSource {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            AxisSource::from(value as u32)
        }
    }

    impl From<AxisSource> for u32 {
        fn from(value: AxisSource) -> Self {
            value.0
        }
    }

    impl From<AxisSource> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: AxisSource) -> Self {
            value.0 as i32
        }
    }

    ///  axis_relative_direction: axis relative direction
    /// 
    ///  
    ///  This specifies the direction of the physical motion that caused a
    ///  wl_pointer.axis event, relative to the wl_pointer.axis direction.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct AxisRelativeDirection(u32);

    impl AxisRelativeDirection {
        /// identical - physical motion matches axis direction
        pub const IDENTICAL: AxisRelativeDirection = AxisRelativeDirection(0);

        /// inverted - physical motion is the inverse of the axis direction
        pub const INVERTED: AxisRelativeDirection = AxisRelativeDirection(1);

    }

    impl From<u32> for AxisRelativeDirection {
        fn from(value: u32) -> Self {
            AxisRelativeDirection(value)
        }
    }

    impl From<i32> for AxisRelativeDirection {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            AxisRelativeDirection::from(value as u32)
        }
    }

    impl From<AxisRelativeDirection> for u32 {
        fn from(value: AxisRelativeDirection) -> Self {
            value.0
        }
    }

    impl From<AxisRelativeDirection> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: AxisRelativeDirection) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_pointer::WlPointer;

pub mod wl_keyboard {
    //!  wl_keyboard: keyboard input device
    //! 
    //!  
    //!  The wl_keyboard interface represents one or more keyboards
    //!  associated with a seat.
    //!  
    //!  Each wl_keyboard has the following logical state:
    //!  
    //!  - an active surface (possibly null),
    //!  - the keys currently logically down,
    //!  - the active modifiers,
    //!  - the active group.
    //!  
    //!  By default, the active surface is null, the keys currently logically down
    //!  are empty, the active modifiers and the active group are 0.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_keyboard` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlKeyboard(Proxy);

    impl fmt::Debug for WlKeyboard {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlKeyboard").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlKeyboard {
        fn from(proxy: Proxy) -> Self {
            WlKeyboard(proxy)
        }
    }

    impl From<WlKeyboard> for Proxy {
        fn from(proxy: WlKeyboard) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlKeyboard {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlKeyboard {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlKeyboard {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlKeyboard {
        pub fn release(
            &mut self,
        ) -> io::Result<()> {
            //!  release: release the keyboard object

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let format = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let format = KeymapFormat::from(format);

                        let fd = match iter.next() {
                            Some(crate::TypedArgument::FileDescriptor(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let size = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Keymap {
                            format,
                            fd,
                            size,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let surface = surface.map(super::wl_surface::WlSurface::from);
                        let surface = surface.unwrap();

                        let keys = match iter.next() {
                            Some(crate::TypedArgument::Array(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Enter {
                            serial,
                            surface: &surface,
                            keys: keys.as_slice(),
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let surface = surface.map(super::wl_surface::WlSurface::from);
                        let surface = surface.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Leave {
                            serial,
                            surface: &surface,
                        };
                        listener(&proxy, event);
                    },
                    3 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let key = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let state = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let state = KeyState::from(state);

                        assert!(iter.next().is_none());
                        let event = Event::Key {
                            serial,
                            time,
                            key,
                            state,
                        };
                        listener(&proxy, event);
                    },
                    4 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let mods_depressed = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let mods_latched = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let mods_locked = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let group = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Modifiers {
                            serial,
                            mods_depressed,
                            mods_latched,
                            mods_locked,
                            group,
                        };
                        listener(&proxy, event);
                    },
                    5 => {
                        let rate = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let delay = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::RepeatInfo {
                            rate,
                            delay,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 10;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  keymap: keyboard mapping
        /// 
        ///  
        ///  This event provides a file descriptor to the client which can be
        ///  memory-mapped in read-only mode to provide a keyboard mapping
        ///  description.
        ///  
        ///  From version 7 onwards, the fd must be mapped with MAP_PRIVATE by
        ///  the recipient, as MAP_SHARED may fail.
        ///  
        Keymap {
            /// format - keymap format
            format: KeymapFormat,
            /// fd - keymap file descriptor
            fd: std::os::unix::io::BorrowedFd<'a>,
            /// size - keymap size, in bytes
            size: u32,
        },
        ///  enter: enter event
        /// 
        ///  
        ///  Notification that this seat's keyboard focus is on a certain
        ///  surface.
        ///  
        ///  The compositor must send the wl_keyboard.modifiers event after this
        ///  event.
        ///  
        ///  In the wl_keyboard logical state, this event sets the active surface to
        ///  the surface argument and the keys currently logically down to the keys
        ///  in the keys argument. The compositor must not send this event if the
        ///  wl_keyboard already had an active surface immediately before this event.
        ///  
        ///  Clients should not use the list of pressed keys to emulate key-press
        ///  events. The order of keys in the list is unspecified.
        ///  
        Enter {
            /// serial - serial number of the enter event
            serial: u32,
            /// surface - surface gaining keyboard focus
            surface: &'a super::wl_surface::WlSurface,
            /// keys - the keys currently logically down
            keys: &'a [u8],
        },
        ///  leave: leave event
        /// 
        ///  
        ///  Notification that this seat's keyboard focus is no longer on
        ///  a certain surface.
        ///  
        ///  The leave notification is sent before the enter notification
        ///  for the new focus.
        ///  
        ///  In the wl_keyboard logical state, this event resets all values to their
        ///  defaults. The compositor must not send this event if the active surface
        ///  of the wl_keyboard was not equal to the surface argument immediately
        ///  before this event.
        ///  
        Leave {
            /// serial - serial number of the leave event
            serial: u32,
            /// surface - surface that lost keyboard focus
            surface: &'a super::wl_surface::WlSurface,
        },
        ///  key: key event
        /// 
        ///  
        ///  A key was pressed or released.
        ///  The time argument is a timestamp with millisecond
        ///  granularity, with an undefined base.
        ///  
        ///  The key is a platform-specific key code that can be interpreted
        ///  by feeding it to the keyboard mapping (see the keymap event).
        ///  
        ///  If this event produces a change in modifiers, then the resulting
        ///  wl_keyboard.modifiers event must be sent after this event.
        ///  
        ///  In the wl_keyboard logical state, this event adds the key to the keys
        ///  currently logically down (if the state argument is pressed) or removes
        ///  the key from the keys currently logically down (if the state argument is
        ///  released). The compositor must not send this event if the wl_keyboard
        ///  did not have an active surface immediately before this event. The
        ///  compositor must not send this event if state is pressed (resp. released)
        ///  and the key was already logically down (resp. was not logically down)
        ///  immediately before this event.
        ///  
        ///  Since version 10, compositors may send key events with the "repeated"
        ///  key state when a wl_keyboard.repeat_info event with a rate argument of
        ///  0 has been received. This allows the compositor to take over the
        ///  responsibility of key repetition.
        ///  
        Key {
            /// serial - serial number of the key event
            serial: u32,
            /// time - timestamp with millisecond granularity
            time: u32,
            /// key - key that produced the event
            key: u32,
            /// state - physical state of the key
            state: KeyState,
        },
        ///  modifiers: modifier and group state
        /// 
        ///  
        ///  Notifies clients that the modifier and/or group state has
        ///  changed, and it should update its local state.
        ///  
        ///  The compositor may send this event without a surface of the client
        ///  having keyboard focus, for example to tie modifier information to
        ///  pointer focus instead. If a modifier event with pressed modifiers is sent
        ///  without a prior enter event, the client can assume the modifier state is
        ///  valid until it receives the next wl_keyboard.modifiers event. In order to
        ///  reset the modifier state again, the compositor can send a
        ///  wl_keyboard.modifiers event with no pressed modifiers.
        ///  
        ///  In the wl_keyboard logical state, this event updates the modifiers and
        ///  group.
        ///  
        Modifiers {
            /// serial - serial number of the modifiers event
            serial: u32,
            /// mods_depressed - depressed modifiers
            mods_depressed: u32,
            /// mods_latched - latched modifiers
            mods_latched: u32,
            /// mods_locked - locked modifiers
            mods_locked: u32,
            /// group - keyboard layout
            group: u32,
        },
        ///  repeat_info: repeat rate and delay
        /// 
        ///  
        ///  Informs the client about the keyboard's repeat rate and delay.
        ///  
        ///  This event is sent as soon as the wl_keyboard object has been created,
        ///  and is guaranteed to be received by the client before any key press
        ///  event.
        ///  
        ///  Negative values for either rate or delay are illegal. A rate of zero
        ///  will disable any repeating (regardless of the value of delay).
        ///  
        ///  This event can be sent later on as well with a new value if necessary,
        ///  so clients should continue listening for the event past the creation
        ///  of wl_keyboard.
        ///  
        RepeatInfo {
            /// rate - the rate of repeating keys in characters per second
            rate: i32,
            /// delay - delay in milliseconds since key down until repeating starts
            delay: i32,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_keyboard"),
        10,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("release"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("keymap"),
            sig!("uhu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("enter"),
            sig!("uoa"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, Some(&super::wl_surface::INTERFACE), None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("leave"),
            sig!("uo"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, Some(&super::wl_surface::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("key"),
            sig!("uuuu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("modifiers"),
            sig!("uuuuu"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("repeat_info"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
    ];

    ///  keymap_format: keyboard mapping format
    /// 
    ///  
    ///  This specifies the format of the keymap provided to the
    ///  client with the wl_keyboard.keymap event.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct KeymapFormat(u32);

    impl KeymapFormat {
        /// no_keymap - no keymap; client must understand how to interpret the raw keycode
        pub const NO_KEYMAP: KeymapFormat = KeymapFormat(0);

        /// xkb_v1 - libxkbcommon compatible, null-terminated string; to determine the xkb keycode, clients must add 8 to the key event keycode
        pub const XKB_V1: KeymapFormat = KeymapFormat(1);

    }

    impl From<u32> for KeymapFormat {
        fn from(value: u32) -> Self {
            KeymapFormat(value)
        }
    }

    impl From<i32> for KeymapFormat {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            KeymapFormat::from(value as u32)
        }
    }

    impl From<KeymapFormat> for u32 {
        fn from(value: KeymapFormat) -> Self {
            value.0
        }
    }

    impl From<KeymapFormat> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: KeymapFormat) -> Self {
            value.0 as i32
        }
    }

    ///  key_state: physical key state
    /// 
    ///  
    ///  Describes the physical state of a key that produced the key event.
    ///  
    ///  Since version 10, the key can be in a "repeated" pseudo-state which
    ///  means the same as "pressed", but is used to signal repetition in the
    ///  key event.
    ///  
    ///  The key may only enter the repeated state after entering the pressed
    ///  state and before entering the released state. This event may be
    ///  generated multiple times while the key is down.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct KeyState(u32);

    impl KeyState {
        /// released - key is not pressed
        pub const RELEASED: KeyState = KeyState(0);

        /// pressed - key is pressed
        pub const PRESSED: KeyState = KeyState(1);

        /// repeated - key was repeated
        pub const REPEATED: KeyState = KeyState(2);

    }

    impl From<u32> for KeyState {
        fn from(value: u32) -> Self {
            KeyState(value)
        }
    }

    impl From<i32> for KeyState {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            KeyState::from(value as u32)
        }
    }

    impl From<KeyState> for u32 {
        fn from(value: KeyState) -> Self {
            value.0
        }
    }

    impl From<KeyState> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: KeyState) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_keyboard::WlKeyboard;

pub mod wl_touch {
    //!  wl_touch: touchscreen input device
    //! 
    //!  
    //!  The wl_touch interface represents a touchscreen
    //!  associated with a seat.
    //!  
    //!  Touch interactions can consist of one or more contacts.
    //!  For each contact, a series of events is generated, starting
    //!  with a down event, followed by zero or more motion events,
    //!  and ending with an up event. Events relating to the same
    //!  contact point can be identified by the ID of the sequence.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_touch` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlTouch(Proxy);

    impl fmt::Debug for WlTouch {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlTouch").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlTouch {
        fn from(proxy: Proxy) -> Self {
            WlTouch(proxy)
        }
    }

    impl From<WlTouch> for Proxy {
        fn from(proxy: WlTouch) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlTouch {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlTouch {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlTouch {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlTouch {
        pub fn release(
            &mut self,
        ) -> io::Result<()> {
            //!  release: release the touch object

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let surface = match iter.next() {
                            Some(crate::TypedArgument::Object(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let surface = surface.map(super::wl_surface::WlSurface::from);
                        let surface = surface.unwrap();

                        let id = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let x = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let y = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Down {
                            serial,
                            time,
                            surface: &surface,
                            id,
                            x,
                            y,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let serial = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let id = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Up {
                            serial,
                            time,
                            id,
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        let time = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let id = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let x = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let y = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Motion {
                            time,
                            id,
                            x,
                            y,
                        };
                        listener(&proxy, event);
                    },
                    3 => {
                        assert!(iter.next().is_none());
                        let event = Event::Frame {
                        };
                        listener(&proxy, event);
                    },
                    4 => {
                        assert!(iter.next().is_none());
                        let event = Event::Cancel {
                        };
                        listener(&proxy, event);
                    },
                    5 => {
                        let id = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let major = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let minor = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Shape {
                            id,
                            major,
                            minor,
                        };
                        listener(&proxy, event);
                    },
                    6 => {
                        let id = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let orientation = match iter.next() {
                            Some(crate::TypedArgument::Float(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Orientation {
                            id,
                            orientation,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 10;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  down: touch down event and beginning of a touch sequence
        /// 
        ///  
        ///  A new touch point has appeared on the surface. This touch point is
        ///  assigned a unique ID. Future events from this touch point reference
        ///  this ID. The ID ceases to be valid after a touch up event and may be
        ///  reused in the future.
        ///  
        Down {
            /// serial - serial number of the touch down event
            serial: u32,
            /// time - timestamp with millisecond granularity
            time: u32,
            /// surface - surface touched
            surface: &'a super::wl_surface::WlSurface,
            /// id - the unique ID of this touch point
            id: i32,
            /// x - surface-local x coordinate
            x: crate::Fixed,
            /// y - surface-local y coordinate
            y: crate::Fixed,
        },
        ///  up: end of a touch event sequence
        /// 
        ///  
        ///  The touch point has disappeared. No further events will be sent for
        ///  this touch point and the touch point's ID is released and may be
        ///  reused in a future touch down event.
        ///  
        Up {
            /// serial - serial number of the touch up event
            serial: u32,
            /// time - timestamp with millisecond granularity
            time: u32,
            /// id - the unique ID of this touch point
            id: i32,
        },
        ///  motion: update of touch point coordinates
        /// 
        ///  
        ///  A touch point has changed coordinates.
        ///  
        Motion {
            /// time - timestamp with millisecond granularity
            time: u32,
            /// id - the unique ID of this touch point
            id: i32,
            /// x - surface-local x coordinate
            x: crate::Fixed,
            /// y - surface-local y coordinate
            y: crate::Fixed,
        },
        ///  frame: end of touch frame event
        /// 
        ///  
        ///  Indicates the end of a set of events that logically belong together.
        ///  A client is expected to accumulate the data in all events within the
        ///  frame before proceeding.
        ///  
        ///  A wl_touch.frame terminates at least one event but otherwise no
        ///  guarantee is provided about the set of events within a frame. A client
        ///  must assume that any state not updated in a frame is unchanged from the
        ///  previously known state.
        ///  
        Frame {
        },
        ///  cancel: touch session cancelled
        /// 
        ///  
        ///  Sent if the compositor decides the touch stream is a global
        ///  gesture. No further events are sent to the clients from that
        ///  particular gesture. Touch cancellation applies to all touch points
        ///  currently active on this client's surface. The client is
        ///  responsible for finalizing the touch points, future touch points on
        ///  this surface may reuse the touch point ID.
        ///  
        ///  No frame event is required after the cancel event.
        ///  
        Cancel {
        },
        ///  shape: update shape of touch point
        /// 
        ///  
        ///  Sent when a touchpoint has changed its shape.
        ///  
        ///  This event does not occur on its own. It is sent before a
        ///  wl_touch.frame event and carries the new shape information for
        ///  any previously reported, or new touch points of that frame.
        ///  
        ///  Other events describing the touch point such as wl_touch.down,
        ///  wl_touch.motion or wl_touch.orientation may be sent within the
        ///  same wl_touch.frame. A client should treat these events as a single
        ///  logical touch point update. The order of wl_touch.shape,
        ///  wl_touch.orientation and wl_touch.motion is not guaranteed.
        ///  A wl_touch.down event is guaranteed to occur before the first
        ///  wl_touch.shape event for this touch ID but both events may occur within
        ///  the same wl_touch.frame.
        ///  
        ///  A touchpoint shape is approximated by an ellipse through the major and
        ///  minor axis length. The major axis length describes the longer diameter
        ///  of the ellipse, while the minor axis length describes the shorter
        ///  diameter. Major and minor are orthogonal and both are specified in
        ///  surface-local coordinates. The center of the ellipse is always at the
        ///  touchpoint location as reported by wl_touch.down or wl_touch.move.
        ///  
        ///  This event is only sent by the compositor if the touch device supports
        ///  shape reports. The client has to make reasonable assumptions about the
        ///  shape if it did not receive this event.
        ///  
        Shape {
            /// id - the unique ID of this touch point
            id: i32,
            /// major - length of the major axis in surface-local coordinates
            major: crate::Fixed,
            /// minor - length of the minor axis in surface-local coordinates
            minor: crate::Fixed,
        },
        ///  orientation: update orientation of touch point
        /// 
        ///  
        ///  Sent when a touchpoint has changed its orientation.
        ///  
        ///  This event does not occur on its own. It is sent before a
        ///  wl_touch.frame event and carries the new shape information for
        ///  any previously reported, or new touch points of that frame.
        ///  
        ///  Other events describing the touch point such as wl_touch.down,
        ///  wl_touch.motion or wl_touch.shape may be sent within the
        ///  same wl_touch.frame. A client should treat these events as a single
        ///  logical touch point update. The order of wl_touch.shape,
        ///  wl_touch.orientation and wl_touch.motion is not guaranteed.
        ///  A wl_touch.down event is guaranteed to occur before the first
        ///  wl_touch.orientation event for this touch ID but both events may occur
        ///  within the same wl_touch.frame.
        ///  
        ///  The orientation describes the clockwise angle of a touchpoint's major
        ///  axis to the positive surface y-axis and is normalized to the -180 to
        ///  +180 degree range. The granularity of orientation depends on the touch
        ///  device, some devices only support binary rotation values between 0 and
        ///  90 degrees.
        ///  
        ///  This event is only sent by the compositor if the touch device supports
        ///  orientation reports.
        ///  
        Orientation {
            /// id - the unique ID of this touch point
            id: i32,
            /// orientation - angle between major axis and positive surface y-axis in degrees
            orientation: crate::Fixed,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_touch"),
        10,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("release"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("down"),
            sig!("uuoiff"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, Some(&super::wl_surface::INTERFACE), None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("up"),
            sig!("uui"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("motion"),
            sig!("uiff"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("frame"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("cancel"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("shape"),
            sig!("iff"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("orientation"),
            sig!("if"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
    ];

}

pub use self::wl_touch::WlTouch;

pub mod wl_output {
    //!  wl_output: compositor output region
    //! 
    //!  
    //!  An output describes part of the compositor geometry.  The
    //!  compositor works in the 'compositor coordinate system' and an
    //!  output corresponds to a rectangular area in that space that is
    //!  actually visible.  This typically corresponds to a monitor that
    //!  displays part of the compositor space.  This object is published
    //!  as global during start up, or when a monitor is hotplugged.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_output` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlOutput(Proxy);

    impl fmt::Debug for WlOutput {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlOutput").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlOutput {
        fn from(proxy: Proxy) -> Self {
            WlOutput(proxy)
        }
    }

    impl From<WlOutput> for Proxy {
        fn from(proxy: WlOutput) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlOutput {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlOutput {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlOutput {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlOutput {
        pub fn release(
            &mut self,
        ) -> io::Result<()> {
            //!  release: release the output object
            //! 
            //!  
            //!  Using this request a client can tell the server that it is not going to
            //!  use the output object anymore.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    0 => {
                        let x = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let y = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let physical_width = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let physical_height = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let subpixel = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let subpixel = Subpixel::from(subpixel);

                        let make = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let make = make.unwrap();

                        let model = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let model = model.unwrap();

                        let transform = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let transform = Transform::from(transform);

                        assert!(iter.next().is_none());
                        let event = Event::Geometry {
                            x,
                            y,
                            physical_width,
                            physical_height,
                            subpixel,
                            make,
                            model,
                            transform,
                        };
                        listener(&proxy, event);
                    },
                    1 => {
                        let flags = match iter.next() {
                            Some(crate::TypedArgument::UInt32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let flags = Mode::from(flags);

                        let width = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let height = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        let refresh = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Mode {
                            flags,
                            width,
                            height,
                            refresh,
                        };
                        listener(&proxy, event);
                    },
                    2 => {
                        assert!(iter.next().is_none());
                        let event = Event::Done {
                        };
                        listener(&proxy, event);
                    },
                    3 => {
                        let factor = match iter.next() {
                            Some(crate::TypedArgument::Int32(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };

                        assert!(iter.next().is_none());
                        let event = Event::Scale {
                            factor,
                        };
                        listener(&proxy, event);
                    },
                    4 => {
                        let name = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let name = name.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Name {
                            name,
                        };
                        listener(&proxy, event);
                    },
                    5 => {
                        let description = match iter.next() {
                            Some(crate::TypedArgument::String(arg)) => arg,
                            _ => panic!("protocol violation"),
                        };
                        let description = description.unwrap();

                        assert!(iter.next().is_none());
                        let event = Event::Description {
                            description,
                        };
                        listener(&proxy, event);
                    },
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 4;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
        ///  geometry: properties of the output
        /// 
        ///  
        ///  The geometry event describes geometric properties of the output.
        ///  The event is sent when binding to the output object and whenever
        ///  any of the properties change.
        ///  
        ///  The physical size can be set to zero if it doesn't make sense for this
        ///  output (e.g. for projectors or virtual outputs).
        ///  
        ///  The geometry event will be followed by a done event (starting from
        ///  version 2).
        ///  
        ///  Clients should use wl_surface.preferred_buffer_transform instead of the
        ///  transform advertised by this event to find the preferred buffer
        ///  transform to use for a surface.
        ///  
        ///  Note: wl_output only advertises partial information about the output
        ///  position and identification. Some compositors, for instance those not
        ///  implementing a desktop-style output layout or those exposing virtual
        ///  outputs, might fake this information. Instead of using x and y, clients
        ///  should use xdg_output.logical_position. Instead of using make and model,
        ///  clients should use name and description.
        ///  
        Geometry {
            /// x - x position within the global compositor space
            x: i32,
            /// y - y position within the global compositor space
            y: i32,
            /// physical_width - width in millimeters of the output
            physical_width: i32,
            /// physical_height - height in millimeters of the output
            physical_height: i32,
            /// subpixel - subpixel orientation of the output
            subpixel: Subpixel,
            /// make - textual description of the manufacturer
            make: &'a CStr,
            /// model - textual description of the model
            model: &'a CStr,
            /// transform - additional transformation applied to buffer contents during presentation
            transform: Transform,
        },
        ///  mode: advertise available modes for the output
        /// 
        ///  
        ///  The mode event describes an available mode for the output.
        ///  
        ///  The event is sent when binding to the output object and there
        ///  will always be one mode, the current mode.  The event is sent
        ///  again if an output changes mode, for the mode that is now
        ///  current.  In other words, the current mode is always the last
        ///  mode that was received with the current flag set.
        ///  
        ///  Non-current modes are deprecated. A compositor can decide to only
        ///  advertise the current mode and never send other modes. Clients
        ///  should not rely on non-current modes.
        ///  
        ///  The size of a mode is given in physical hardware units of
        ///  the output device. This is not necessarily the same as
        ///  the output size in the global compositor space. For instance,
        ///  the output may be scaled, as described in wl_output.scale,
        ///  or transformed, as described in wl_output.transform. Clients
        ///  willing to retrieve the output size in the global compositor
        ///  space should use xdg_output.logical_size instead.
        ///  
        ///  The vertical refresh rate can be set to zero if it doesn't make
        ///  sense for this output (e.g. for virtual outputs).
        ///  
        ///  The mode event will be followed by a done event (starting from
        ///  version 2).
        ///  
        ///  Clients should not use the refresh rate to schedule frames. Instead,
        ///  they should use the wl_surface.frame event or the presentation-time
        ///  protocol.
        ///  
        ///  Note: this information is not always meaningful for all outputs. Some
        ///  compositors, such as those exposing virtual outputs, might fake the
        ///  refresh rate or the size.
        ///  
        Mode {
            /// flags - bitfield of mode flags
            flags: Mode,
            /// width - width of the mode in hardware units
            width: i32,
            /// height - height of the mode in hardware units
            height: i32,
            /// refresh - vertical refresh rate in mHz
            refresh: i32,
        },
        ///  done: sent all information about output
        /// 
        ///  
        ///  This event is sent after all other properties have been
        ///  sent after binding to the output object and after any
        ///  other property changes done after that. This allows
        ///  changes to the output properties to be seen as
        ///  atomic, even if they happen via multiple events.
        ///  
        Done {
        },
        ///  scale: output scaling properties
        /// 
        ///  
        ///  This event contains scaling geometry information
        ///  that is not in the geometry event. It may be sent after
        ///  binding the output object or if the output scale changes
        ///  later. The compositor will emit a non-zero, positive
        ///  value for scale. If it is not sent, the client should
        ///  assume a scale of 1.
        ///  
        ///  A scale larger than 1 means that the compositor will
        ///  automatically scale surface buffers by this amount
        ///  when rendering. This is used for very high resolution
        ///  displays where applications rendering at the native
        ///  resolution would be too small to be legible.
        ///  
        ///  Clients should use wl_surface.preferred_buffer_scale
        ///  instead of this event to find the preferred buffer
        ///  scale to use for a surface.
        ///  
        ///  The scale event will be followed by a done event.
        ///  
        Scale {
            /// factor - scaling factor of output
            factor: i32,
        },
        ///  name: name of this output
        /// 
        ///  
        ///  Many compositors will assign user-friendly names to their outputs, show
        ///  them to the user, allow the user to refer to an output, etc. The client
        ///  may wish to know this name as well to offer the user similar behaviors.
        ///  
        ///  The name is a UTF-8 string with no convention defined for its contents.
        ///  Each name is unique among all wl_output globals. The name is only
        ///  guaranteed to be unique for the compositor instance.
        ///  
        ///  The same output name is used for all clients for a given wl_output
        ///  global. Thus, the name can be shared across processes to refer to a
        ///  specific wl_output global.
        ///  
        ///  The name is not guaranteed to be persistent across sessions, thus cannot
        ///  be used to reliably identify an output in e.g. configuration files.
        ///  
        ///  Examples of names include 'HDMI-A-1', 'WL-1', 'X11-1', etc. However, do
        ///  not assume that the name is a reflection of an underlying DRM connector,
        ///  X11 connection, etc.
        ///  
        ///  The name event is sent after binding the output object. This event is
        ///  only sent once per output object, and the name does not change over the
        ///  lifetime of the wl_output global.
        ///  
        ///  Compositors may re-use the same output name if the wl_output global is
        ///  destroyed and re-created later. Compositors should avoid re-using the
        ///  same name if possible.
        ///  
        ///  The name event will be followed by a done event.
        ///  
        Name {
            /// name - output name
            name: &'a CStr,
        },
        ///  description: human-readable description of this output
        /// 
        ///  
        ///  Many compositors can produce human-readable descriptions of their
        ///  outputs. The client may wish to know this description as well, e.g. for
        ///  output selection purposes.
        ///  
        ///  The description is a UTF-8 string with no convention defined for its
        ///  contents. The description is not guaranteed to be unique among all
        ///  wl_output globals. Examples might include 'Foocorp 11" Display' or
        ///  'Virtual X11 output via :1'.
        ///  
        ///  The description event is sent after binding the output object and
        ///  whenever the description changes. The description is optional, and may
        ///  not be sent at all.
        ///  
        ///  The description event will be followed by a done event.
        ///  
        Description {
            /// description - output description
            description: &'a CStr,
        },
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_output"),
        4,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("release"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("geometry"),
            sig!("iiiiissi"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("mode"),
            sig!("uiii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("done"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("scale"),
            sig!("i"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("name"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("description"),
            sig!("s"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, ];
                TYPES
            }
        ),
    ];

    ///  subpixel: subpixel geometry information
    /// 
    ///  
    ///  This enumeration describes how the physical
    ///  pixels on an output are laid out.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Subpixel(u32);

    impl Subpixel {
        /// unknown - unknown geometry
        pub const UNKNOWN: Subpixel = Subpixel(0);

        /// none - no geometry
        pub const NONE: Subpixel = Subpixel(1);

        /// horizontal_rgb - horizontal RGB
        pub const HORIZONTAL_RGB: Subpixel = Subpixel(2);

        /// horizontal_bgr - horizontal BGR
        pub const HORIZONTAL_BGR: Subpixel = Subpixel(3);

        /// vertical_rgb - vertical RGB
        pub const VERTICAL_RGB: Subpixel = Subpixel(4);

        /// vertical_bgr - vertical BGR
        pub const VERTICAL_BGR: Subpixel = Subpixel(5);

    }

    impl From<u32> for Subpixel {
        fn from(value: u32) -> Self {
            Subpixel(value)
        }
    }

    impl From<i32> for Subpixel {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Subpixel::from(value as u32)
        }
    }

    impl From<Subpixel> for u32 {
        fn from(value: Subpixel) -> Self {
            value.0
        }
    }

    impl From<Subpixel> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Subpixel) -> Self {
            value.0 as i32
        }
    }

    ///  transform: transformation applied to buffer contents
    /// 
    ///  
    ///  This describes transformations that clients and compositors apply to
    ///  buffer contents.
    ///  
    ///  The flipped values correspond to an initial flip around a
    ///  vertical axis followed by rotation.
    ///  
    ///  The purpose is mainly to allow clients to render accordingly and
    ///  tell the compositor, so that for fullscreen surfaces, the
    ///  compositor will still be able to scan out directly from client
    ///  surfaces.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Transform(u32);

    impl Transform {
        /// normal - no transform
        pub const NORMAL: Transform = Transform(0);

        /// 90 - 90 degrees counter-clockwise
        pub const NINETY: Transform = Transform(1);

        /// 180 - 180 degrees counter-clockwise
        pub const ONE_HUNDRED_EIGHTY: Transform = Transform(2);

        /// 270 - 270 degrees counter-clockwise
        pub const TWO_HUNDRED_SEVENTY: Transform = Transform(3);

        /// flipped - 180 degree flip around a vertical axis
        pub const FLIPPED: Transform = Transform(4);

        /// flipped_90 - flip and rotate 90 degrees counter-clockwise
        pub const FLIPPED_90: Transform = Transform(5);

        /// flipped_180 - flip and rotate 180 degrees counter-clockwise
        pub const FLIPPED_180: Transform = Transform(6);

        /// flipped_270 - flip and rotate 270 degrees counter-clockwise
        pub const FLIPPED_270: Transform = Transform(7);

    }

    impl From<u32> for Transform {
        fn from(value: u32) -> Self {
            Transform(value)
        }
    }

    impl From<i32> for Transform {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Transform::from(value as u32)
        }
    }

    impl From<Transform> for u32 {
        fn from(value: Transform) -> Self {
            value.0
        }
    }

    impl From<Transform> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Transform) -> Self {
            value.0 as i32
        }
    }

    ///  mode: mode information
    /// 
    ///  
    ///  These flags describe properties of an output mode.
    ///  They are used in the flags bitfield of the mode event.
    ///  
    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Mode(u32);

    impl Mode {
        /// current - indicates this is the current mode
        pub const CURRENT: Mode = Mode(0x1);

        /// preferred - indicates this is the preferred mode
        pub const PREFERRED: Mode = Mode(0x2);

    }

    impl From<u32> for Mode {
        fn from(value: u32) -> Self {
            Mode(value)
        }
    }

    impl From<i32> for Mode {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Mode::from(value as u32)
        }
    }

    impl From<Mode> for u32 {
        fn from(value: Mode) -> Self {
            value.0
        }
    }

    impl From<Mode> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Mode) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_output::WlOutput;

pub mod wl_region {
    //!  wl_region: region interface
    //! 
    //!  
    //!  A region object describes an area.
    //!  
    //!  Region objects are used to describe the opaque and input
    //!  regions of a surface.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_region` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlRegion(Proxy);

    impl fmt::Debug for WlRegion {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlRegion").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlRegion {
        fn from(proxy: Proxy) -> Self {
            WlRegion(proxy)
        }
    }

    impl From<WlRegion> for Proxy {
        fn from(proxy: WlRegion) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlRegion {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlRegion {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlRegion {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlRegion {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroy region
            //! 
            //!  
            //!  Destroy the region.  This will invalidate the object ID.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn add(
            &mut self,
            x: i32,
            y: i32,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  add: add rectangle to region
            //! 
            //!  
            //!  Add the specified rectangle to the region.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn subtract(
            &mut self,
            x: i32,
            y: i32,
            width: i32,
            height: i32,
        ) -> io::Result<()> {
            //!  subtract: subtract rectangle from region
            //! 
            //!  
            //!  Subtract the specified rectangle from the region.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
                (Int32, width),
                (Int32, height),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_region"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("add"),
            sig!("iiii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("subtract"),
            sig!("iiii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, None, None, ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

}

pub use self::wl_region::WlRegion;

pub mod wl_subcompositor {
    //!  wl_subcompositor: sub-surface compositing
    //! 
    //!  
    //!  The global interface exposing sub-surface compositing capabilities.
    //!  A wl_surface, that has sub-surfaces associated, is called the
    //!  parent surface. Sub-surfaces can be arbitrarily nested and create
    //!  a tree of sub-surfaces.
    //!  
    //!  The root surface in a tree of sub-surfaces is the main
    //!  surface. The main surface cannot be a sub-surface, because
    //!  sub-surfaces must always have a parent.
    //!  
    //!  A main surface with its sub-surfaces forms a (compound) window.
    //!  For window management purposes, this set of wl_surface objects is
    //!  to be considered as a single window, and it should also behave as
    //!  such.
    //!  
    //!  The aim of sub-surfaces is to offload some of the compositing work
    //!  within a window from clients to the compositor. A prime example is
    //!  a video player with decorations and video in separate wl_surface
    //!  objects. This should allow the compositor to pass YUV video buffer
    //!  processing to dedicated overlay hardware when possible.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_subcompositor` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlSubcompositor(Proxy);

    impl fmt::Debug for WlSubcompositor {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlSubcompositor").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlSubcompositor {
        fn from(proxy: Proxy) -> Self {
            WlSubcompositor(proxy)
        }
    }

    impl From<WlSubcompositor> for Proxy {
        fn from(proxy: WlSubcompositor) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlSubcompositor {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlSubcompositor {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlSubcompositor {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlSubcompositor {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: unbind from the subcompositor interface
            //! 
            //!  
            //!  Informs the server that the client will not be using this
            //!  protocol object anymore. This does not affect any other
            //!  objects, wl_subsurface objects included.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn get_subsurface(
            &mut self,
            event_queue: &crate::EventQueue,
            surface: &super::wl_surface::WlSurface, 
            parent: &super::wl_surface::WlSurface, 
        ) -> io::Result<super::wl_subsurface::WlSubsurface> {
            //!  get_subsurface: give a surface the role sub-surface
            //! 
            //!  
            //!  Create a sub-surface interface for the given surface, and
            //!  associate it with the given parent surface. This turns a
            //!  plain wl_surface into a sub-surface.
            //!  
            //!  The to-be sub-surface must not already have another role, and it
            //!  must not have an existing wl_subsurface object. Otherwise the
            //!  bad_surface protocol error is raised.
            //!  
            //!  Adding sub-surfaces to a parent is a double-buffered operation on the
            //!  parent (see wl_surface.commit). The effect of adding a sub-surface
            //!  becomes visible on the next time the state of the parent surface is
            //!  applied.
            //!  
            //!  The parent surface must not be one of the child surface's descendants,
            //!  and the parent must be different from the child surface, otherwise the
            //!  bad_parent protocol error is raised.
            //!  
            //!  This request modifies the behaviour of wl_surface.commit request on
            //!  the sub-surface, see the documentation on wl_subsurface interface.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (NewId, 0),
                (Object, Some(surface.as_ref())),
                (Object, Some(parent.as_ref())),
            );
            let proxy = self.0.send_message_constructor(
                OPCODE,
                &args,
                super::wl_subsurface::WlSubsurface::INTERFACE,
                super::wl_subsurface::WlSubsurface::VERSION,
                event_queue
            )?;
            Ok(super::wl_subsurface::WlSubsurface::from(proxy))
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_subcompositor"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("get_subsurface"),
            sig!("noo"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_subsurface::INTERFACE), Some(&super::wl_surface::INTERFACE), Some(&super::wl_surface::INTERFACE), ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// bad_surface - the to-be sub-surface is invalid
        pub const BAD_SURFACE: Error = Error(0);

        /// bad_parent - the to-be sub-surface parent is invalid
        pub const BAD_PARENT: Error = Error(1);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_subcompositor::WlSubcompositor;

pub mod wl_subsurface {
    //!  wl_subsurface: sub-surface interface to a wl_surface
    //! 
    //!  
    //!  An additional interface to a wl_surface object, which has been
    //!  made a sub-surface. A sub-surface has one parent surface. A
    //!  sub-surface's size and position are not limited to that of the parent.
    //!  Particularly, a sub-surface is not automatically clipped to its
    //!  parent's area.
    //!  
    //!  A sub-surface becomes mapped, when a non-NULL wl_buffer is applied
    //!  and the parent surface is mapped. The order of which one happens
    //!  first is irrelevant. A sub-surface is hidden if the parent becomes
    //!  hidden, or if a NULL wl_buffer is applied. These rules apply
    //!  recursively through the tree of surfaces.
    //!  
    //!  The behaviour of a wl_surface.commit request on a sub-surface
    //!  depends on the sub-surface's mode. The possible modes are
    //!  synchronized and desynchronized, see methods
    //!  wl_subsurface.set_sync and wl_subsurface.set_desync. Synchronized
    //!  mode caches the wl_surface state to be applied when the parent's
    //!  state gets applied, and desynchronized mode applies the pending
    //!  wl_surface state directly. A sub-surface is initially in the
    //!  synchronized mode.
    //!  
    //!  Sub-surfaces also have another kind of state, which is managed by
    //!  wl_subsurface requests, as opposed to wl_surface requests. This
    //!  state includes the sub-surface position relative to the parent
    //!  surface (wl_subsurface.set_position), and the stacking order of
    //!  the parent and its sub-surfaces (wl_subsurface.place_above and
    //!  .place_below). This state is applied when the parent surface's
    //!  wl_surface state is applied, regardless of the sub-surface's mode.
    //!  As the exception, set_sync and set_desync are effective immediately.
    //!  
    //!  The main surface can be thought to be always in desynchronized mode,
    //!  since it does not have a parent in the sub-surfaces sense.
    //!  
    //!  Even if a sub-surface is in desynchronized mode, it will behave as
    //!  in synchronized mode, if its parent surface behaves as in
    //!  synchronized mode. This rule is applied recursively throughout the
    //!  tree of surfaces. This means, that one can set a sub-surface into
    //!  synchronized mode, and then assume that all its child and grand-child
    //!  sub-surfaces are synchronized, too, without explicitly setting them.
    //!  
    //!  Destroying a sub-surface takes effect immediately. If you need to
    //!  synchronize the removal of a sub-surface to the parent surface update,
    //!  unmap the sub-surface first by attaching a NULL wl_buffer, update parent,
    //!  and then destroy the sub-surface.
    //!  
    //!  If the parent wl_surface object is destroyed, the sub-surface is
    //!  unmapped.
    //!  
    //!  A sub-surface never has the keyboard focus of any seat.
    //!  
    //!  The wl_surface.offset request is ignored: clients must use set_position
    //!  instead to move the sub-surface.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_subsurface` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlSubsurface(Proxy);

    impl fmt::Debug for WlSubsurface {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlSubsurface").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlSubsurface {
        fn from(proxy: Proxy) -> Self {
            WlSubsurface(proxy)
        }
    }

    impl From<WlSubsurface> for Proxy {
        fn from(proxy: WlSubsurface) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlSubsurface {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlSubsurface {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlSubsurface {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlSubsurface {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: remove sub-surface interface
            //! 
            //!  
            //!  The sub-surface interface is removed from the wl_surface object
            //!  that was turned into a sub-surface with a
            //!  wl_subcompositor.get_subsurface request. The wl_surface's association
            //!  to the parent is deleted. The wl_surface is unmapped immediately.
            //!  

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_position(
            &mut self,
            x: i32,
            y: i32,
        ) -> io::Result<()> {
            //!  set_position: reposition the sub-surface
            //! 
            //!  
            //!  This schedules a sub-surface position change.
            //!  The sub-surface will be moved so that its origin (top left
            //!  corner pixel) will be at the location x, y of the parent surface
            //!  coordinate system. The coordinates are not restricted to the parent
            //!  surface area. Negative values are allowed.
            //!  
            //!  The scheduled coordinates will take effect whenever the state of the
            //!  parent surface is applied.
            //!  
            //!  If more than one set_position request is invoked by the client before
            //!  the commit of the parent surface, the position of a new request always
            //!  replaces the scheduled position from any previous request.
            //!  
            //!  The initial position is 0, 0.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (Int32, x),
                (Int32, y),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn place_above(
            &mut self,
            sibling: &super::wl_surface::WlSurface, 
        ) -> io::Result<()> {
            //!  place_above: restack the sub-surface
            //! 
            //!  
            //!  This sub-surface is taken from the stack, and put back just
            //!  above the reference surface, changing the z-order of the sub-surfaces.
            //!  The reference surface must be one of the sibling surfaces, or the
            //!  parent surface. Using any other surface, including this sub-surface,
            //!  will cause a protocol error.
            //!  
            //!  The z-order is double-buffered. Requests are handled in order and
            //!  applied immediately to a pending state. The final pending state is
            //!  copied to the active state the next time the state of the parent
            //!  surface is applied.
            //!  
            //!  A new sub-surface is initially added as the top-most in the stack
            //!  of its siblings and parent.
            //!  

            const OPCODE: u32 = 2;

            crate::args!(args = 
                (Object, Some(sibling.as_ref())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn place_below(
            &mut self,
            sibling: &super::wl_surface::WlSurface, 
        ) -> io::Result<()> {
            //!  place_below: restack the sub-surface
            //! 
            //!  
            //!  The sub-surface is placed just below the reference surface.
            //!  See wl_subsurface.place_above.
            //!  

            const OPCODE: u32 = 3;

            crate::args!(args = 
                (Object, Some(sibling.as_ref())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_sync(
            &mut self,
        ) -> io::Result<()> {
            //!  set_sync: set sub-surface to synchronized mode
            //! 
            //!  
            //!  Change the commit behaviour of the sub-surface to synchronized
            //!  mode, also described as the parent dependent mode.
            //!  
            //!  In synchronized mode, wl_surface.commit on a sub-surface will
            //!  accumulate the committed state in a cache, but the state will
            //!  not be applied and hence will not change the compositor output.
            //!  The cached state is applied to the sub-surface immediately after
            //!  the parent surface's state is applied. This ensures atomic
            //!  updates of the parent and all its synchronized sub-surfaces.
            //!  Applying the cached state will invalidate the cache, so further
            //!  parent surface commits do not (re-)apply old state.
            //!  
            //!  See wl_subsurface for the recursive effect of this mode.
            //!  

            const OPCODE: u32 = 4;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn set_desync(
            &mut self,
        ) -> io::Result<()> {
            //!  set_desync: set sub-surface to desynchronized mode
            //! 
            //!  
            //!  Change the commit behaviour of the sub-surface to desynchronized
            //!  mode, also described as independent or freely running mode.
            //!  
            //!  In desynchronized mode, wl_surface.commit on a sub-surface will
            //!  apply the pending state directly, without caching, as happens
            //!  normally with a wl_surface. Calling wl_surface.commit on the
            //!  parent surface has no effect on the sub-surface's wl_surface
            //!  state. This mode allows a sub-surface to be updated on its own.
            //!  
            //!  If cached state exists when wl_surface.commit is called in
            //!  desynchronized mode, the pending state is added to the cached
            //!  state, and applied as a whole. This invalidates the cache.
            //!  
            //!  Note: even if a sub-surface is set to desynchronized, a parent
            //!  sub-surface may override it to behave as synchronized. For details,
            //!  see wl_subsurface.
            //!  
            //!  If a surface's parent surface behaves as desynchronized, then
            //!  the cached state is applied on set_desync.
            //!  

            const OPCODE: u32 = 5;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_subsurface"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_position"),
            sig!("ii"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[None, None, ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("place_above"),
            sig!("o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_surface::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("place_below"),
            sig!("o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_surface::INTERFACE), ];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_sync"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("set_desync"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

    #[allow(missing_docs)]
    #[derive(Debug, Copy, Clone, PartialEq, Eq)]
    pub struct Error(u32);

    impl Error {
        /// bad_surface - wl_surface is not a sibling or the parent
        pub const BAD_SURFACE: Error = Error(0);

    }

    impl From<u32> for Error {
        fn from(value: u32) -> Self {
            Error(value)
        }
    }

    impl From<i32> for Error {
        #[allow(clippy::cast_sign_loss)]
        fn from(value: i32) -> Self {
            Error::from(value as u32)
        }
    }

    impl From<Error> for u32 {
        fn from(value: Error) -> Self {
            value.0
        }
    }

    impl From<Error> for i32 {
        #[allow(clippy::cast_possible_wrap)]
        fn from(value: Error) -> Self {
            value.0 as i32
        }
    }

}

pub use self::wl_subsurface::WlSubsurface;

pub mod wl_fixes {
    //!  wl_fixes: wayland protocol fixes
    //! 
    //!  
    //!  This global fixes problems with other core-protocol interfaces that
    //!  cannot be fixed in these interfaces themselves.
    //!  

    use crate::Proxy;
    use std::ffi::CStr;
    use std::fmt;
    use std::io;
    use std::os::unix::io::AsFd;

    /// Wrapper around the `wl_fixes` interface.
    ///
    /// See [top-level documentation](super::mod.rs) for more info.
    pub struct WlFixes(Proxy);

    impl fmt::Debug for WlFixes {
        fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
            f.debug_tuple("WlFixes").field(&self.0).finish()
        }
    }

    impl From<Proxy> for WlFixes {
        fn from(proxy: Proxy) -> Self {
            WlFixes(proxy)
        }
    }

    impl From<WlFixes> for Proxy {
        fn from(proxy: WlFixes) -> Self {
            proxy.0
        }
    }

    impl AsRef<Proxy> for WlFixes {
        fn as_ref(&self) -> &Proxy {
            &self.0
        }
    }

    impl AsMut<Proxy> for WlFixes {
        fn as_mut(&mut self) -> &mut Proxy {
            &mut self.0
        }
    }

    impl WlFixes {
        /// Get a reference to the underlying proxy.
        #[inline]
        #[must_use]
        pub fn as_proxy(&self) -> &Proxy {
            &self.0
        }
    }

    impl WlFixes {
        pub fn destroy(
            &mut self,
        ) -> io::Result<()> {
            //!  destroy: destroys this object

            const OPCODE: u32 = 0;

            crate::args!(args = 
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        pub fn destroy_registry(
            &mut self,
            registry: &super::wl_registry::WlRegistry, 
        ) -> io::Result<()> {
            //!  destroy_registry: destroy a wl_registry
            //! 
            //!  
            //!  This request destroys a wl_registry object.
            //!  
            //!  The client should no longer use the wl_registry after making this
            //!  request.
            //!  
            //!  The compositor will emit a wl_display.delete_id event with the object ID
            //!  of the registry and will no longer emit any events on the registry. The
            //!  client should re-use the object ID once it receives the
            //!  wl_display.delete_id event.
            //!  

            const OPCODE: u32 = 1;

            crate::args!(args = 
                (Object, Some(registry.as_ref())),
            );
            self.0.send_message(OPCODE, &args)?;
            Ok(())
        }

        /// Set a listener for incoming events.
        pub fn add_listener<F>(&self, mut listener: F) -> io::Result<()>
        where F: FnMut(&Self, Event<'_>) + 'static
        {
            self.0.add_listener(move |proxy, opcode, incoming| {
                let proxy: Self = proxy.into();
                let mut iter = incoming.iter(proxy.as_ref());
                match opcode {
                    _ => { let _ = (&listener, iter); }
                }
            })
        }


        /// The latest known version for this proxy.
        pub const VERSION: u32 = 1;

        /// The interface for this proxy.
        pub const INTERFACE: &'static crate::Interface = &INTERFACE;
    }

    /// Sum type containing all possible events for this interface.
    #[non_exhaustive]
    #[derive(Debug)]
    pub enum Event<'a> {
        #[doc(hidden)]
        __Lifetime(std::marker::PhantomData<&'a ()>),
    }

    pub(crate) static INTERFACE: crate::Interface = crate::Interface::new(
        cstr!("wl_fixes"),
        1,
        REQUESTS,
        EVENTS,
    );

    static REQUESTS: &[crate::Message] = &[
        crate::Message::new(
            cstr!("destroy"),
            sig!(""),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[];
                TYPES
            }
        ),
        crate::Message::new(
            cstr!("destroy_registry"),
            sig!("o"),
            {
                static TYPES: &[Option<&'static crate::Interface>] = &[Some(&super::wl_registry::INTERFACE), ];
                TYPES
            }
        ),
    ];

    static EVENTS: &[crate::Message] = &[
    ];

}

pub use self::wl_fixes::WlFixes;

